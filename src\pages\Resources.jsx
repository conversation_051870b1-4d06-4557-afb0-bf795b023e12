import React, { useState } from "react";
import { resources } from "@/data/resources";
import { Download, FileText, Calculator, CheckSquare, BookOpen, Video, Star, Users } from "lucide-react";

export default function Resources() {
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [emailModalOpen, setEmailModalOpen] = useState(false);
  const [selectedResource, setSelectedResource] = useState(null);

  const categories = [
    { value: "all", label: "ALL RESOURCES" },
    { value: "strategy", label: "STRATEGY" },
    { value: "advertising", label: "ADVERTISING" },
    { value: "analytics", label: "ANALYTICS" },
    { value: "content", label: "CONTENT" },
    { value: "social-media", label: "SOCIAL MEDIA" }
  ];

  const getTypeIcon = (type) => {
    const icons = {
      template: FileText,
      checklist: CheckSquare,
      calculator: Calculator,
      guide: <PERSON><PERSON><PERSON>,
      ebook: Book<PERSON><PERSON>,
      webinar: Video
    };
    return icons[type] || FileText;
  };

  const getTypeColor = (type) => {
    const colors = {
      template: "bg-blue-500",
      checklist: "bg-green-400",
      calculator: "bg-pink-500",
      guide: "bg-yellow-400",
      ebook: "bg-purple-500",
      webinar: "bg-red-500"
    };
    return colors[type] || "bg-gray-500";
  };

  const filteredResources = selectedCategory === "all" 
    ? resources 
    : resources.filter(resource => resource.category === selectedCategory);

  const featuredResources = resources.filter(resource => resource.featured);

  const handleDownload = (resource) => {
    if (resource.email_required) {
      setSelectedResource(resource);
      setEmailModalOpen(true);
    } else {
      // Direct download
      console.log("Downloading:", resource.title);
    }
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="py-20 bg-pink-500">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="anti-asymmetric">
            <div className="bg-white text-black p-8 brutal-border brutal-shadow inline-block">
              <h1 className="brutal-text text-4xl md:text-6xl mb-4">FREE RESOURCES</h1>
              <p className="text-xl font-bold max-w-2xl mx-auto">
                Templates, guides, and tools to help you dominate social media marketing.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Resources */}
      <section className="py-20 bg-yellow-400">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <div className="bg-black text-white p-4 brutal-border brutal-shadow inline-block transform rotate-1">
              <h2 className="brutal-text text-2xl md:text-3xl">FEATURED RESOURCES</h2>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {featuredResources.map((resource, index) => {
              const IconComponent = getTypeIcon(resource.type);
              return (
                <div 
                  key={resource.id} 
                  className={`${index % 2 === 0 ? 'asymmetric-grid' : 'anti-asymmetric'}`}
                >
                  <div className="bg-white brutal-border brutal-shadow p-8 hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-300">
                    <div className="flex items-start gap-4 mb-6">
                      <div className={`${getTypeColor(resource.type)} w-16 h-16 brutal-border brutal-shadow-small flex items-center justify-center`}>
                        <IconComponent className="w-8 h-8 text-white" />
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <span className="bg-black text-white px-2 py-1 brutal-text text-xs">
                            {resource.type.toUpperCase()}
                          </span>
                          <Star className="w-4 h-4 text-yellow-500 fill-current" />
                        </div>
                        <h3 className="brutal-text text-xl mb-2">{resource.title}</h3>
                      </div>
                    </div>
                    
                    <p className="font-bold text-gray-700 mb-6">
                      {resource.description}
                    </p>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4 text-sm font-bold text-gray-600">
                        <div className="flex items-center gap-1">
                          <Users className="w-4 h-4" />
                          <span>{resource.download_count.toLocaleString()} downloads</span>
                        </div>
                      </div>
                      
                      <button
                        onClick={() => handleDownload(resource)}
                        className="bg-pink-500 text-white px-6 py-3 brutal-border brutal-shadow brutal-text text-sm hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150 inline-flex items-center gap-2"
                      >
                        <Download className="w-4 h-4" />
                        {resource.email_required ? 'GET FREE ACCESS' : 'DOWNLOAD'}
                      </button>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Category Filter */}
      <section className="py-12 bg-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-wrap justify-center gap-4">
            {categories.map((category) => (
              <button
                key={category.value}
                onClick={() => setSelectedCategory(category.value)}
                className={`px-6 py-3 brutal-text text-sm brutal-border brutal-shadow-small transition-all duration-150 hover:translate-x-1 hover:translate-y-1 hover:shadow-none ${
                  selectedCategory === category.value
                    ? 'bg-blue-500 text-white'
                    : 'bg-white text-black hover:bg-yellow-400'
                }`}
              >
                {category.label}
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* All Resources Grid */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredResources.map((resource, index) => {
              const IconComponent = getTypeIcon(resource.type);
              return (
                <div 
                  key={resource.id} 
                  className={`${index % 2 === 0 ? 'asymmetric-grid' : 'anti-asymmetric'}`}
                >
                  <div className="bg-white brutal-border brutal-shadow p-6 hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-300 h-full flex flex-col">
                    <div className="flex items-center gap-3 mb-4">
                      <div className={`${getTypeColor(resource.type)} w-12 h-12 brutal-border brutal-shadow-small flex items-center justify-center`}>
                        <IconComponent className="w-6 h-6 text-white" />
                      </div>
                      <span className="bg-gray-200 text-black px-2 py-1 brutal-text text-xs">
                        {resource.type.toUpperCase()}
                      </span>
                    </div>
                    
                    <h3 className="brutal-text text-lg mb-3">{resource.title}</h3>
                    
                    <p className="font-bold text-gray-600 mb-4 flex-1">
                      {resource.description}
                    </p>

                    <div className="flex items-center justify-between">
                      <div className="text-sm font-bold text-gray-500">
                        {resource.download_count.toLocaleString()} downloads
                      </div>
                      
                      <button
                        onClick={() => handleDownload(resource)}
                        className="bg-blue-500 text-white px-4 py-2 brutal-text text-xs hover:bg-pink-500 transition-colors duration-200 inline-flex items-center gap-1"
                      >
                        <Download className="w-3 h-3" />
                        {resource.email_required ? 'GET ACCESS' : 'DOWNLOAD'}
                      </button>
                    </div>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      </section>

      {/* Email Modal */}
      {emailModalOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white brutal-border brutal-shadow max-w-md w-full p-8">
            <h3 className="brutal-text text-2xl mb-4">GET FREE ACCESS</h3>
            <p className="font-bold text-gray-700 mb-6">
              Enter your email to download: {selectedResource?.title}
            </p>
            
            <div className="space-y-4">
              <input
                type="email"
                placeholder="Enter your email"
                className="w-full px-4 py-3 brutal-border brutal-shadow-small font-bold focus:outline-none focus:translate-x-1 focus:translate-y-1 focus:shadow-none transition-all duration-150"
              />
              
              <div className="flex gap-4">
                <button
                  onClick={() => setEmailModalOpen(false)}
                  className="flex-1 bg-gray-200 text-black px-4 py-3 brutal-border brutal-shadow brutal-text hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150"
                >
                  CANCEL
                </button>
                <button
                  onClick={() => {
                    // Handle email submission
                    console.log("Email submitted for:", selectedResource?.title);
                    setEmailModalOpen(false);
                  }}
                  className="flex-1 bg-pink-500 text-white px-4 py-3 brutal-border brutal-shadow brutal-text hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150"
                >
                  DOWNLOAD
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
