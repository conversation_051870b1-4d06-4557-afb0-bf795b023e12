import React, { useState, useEffect } from 'react';
import { galleryImages, galleryCategories, getImagesByCategory } from '@/data/galleryImages';
import { Card } from '@/components/common';
import { Filter, Eye, ExternalLink } from 'lucide-react';

export default function Gallery() {
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [filteredImages, setFilteredImages] = useState(galleryImages);
  const [selectedImage, setSelectedImage] = useState(null);
  const [imagesLoaded, setImagesLoaded] = useState({});

  useEffect(() => {
    setFilteredImages(getImagesByCategory(selectedCategory));
  }, [selectedCategory]);

  const handleImageLoad = (imageId) => {
    setImagesLoaded(prev => ({ ...prev, [imageId]: true }));
  };

  const openImageModal = (image) => {
    setSelectedImage(image);
  };

  const closeImageModal = () => {
    setSelectedImage(null);
  };

  const getSizeClasses = (size) => {
    switch (size) {
      case 'large':
        return 'md:col-span-2 md:row-span-2 h-64 md:h-96';
      case 'wide':
        return 'md:col-span-2 h-48 md:h-64';
      case 'tall':
        return 'md:row-span-2 h-80 md:h-96';
      case 'square':
        return 'aspect-square h-64';
      default:
        return 'h-64';
    }
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-purple-500 to-pink-500 py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-white text-black p-8 brutal-border brutal-shadow inline-block asymmetric-grid">
            <h1 className="brutal-text text-4xl md:text-6xl mb-4">OUR GALLERY</h1>
            <p className="text-xl font-bold max-w-2xl">
              Explore our portfolio of successful projects across software development, 
              media production, graphic design, and digital marketing.
            </p>
          </div>
        </div>
      </section>

      {/* Filter Section */}
      <section className="py-12 bg-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-8">
            <div className="bg-yellow-400 text-black p-4 brutal-border brutal-shadow inline-block anti-asymmetric">
              <h2 className="brutal-text text-2xl flex items-center gap-2">
                <Filter className="w-6 h-6" />
                FILTER PROJECTS
              </h2>
            </div>
          </div>
          
          <div className="flex flex-wrap justify-center gap-4">
            {galleryCategories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`px-6 py-3 brutal-border brutal-shadow brutal-text text-sm transition-all duration-150 hover:translate-x-1 hover:translate-y-1 hover:shadow-none ${
                  selectedCategory === category.id
                    ? `${category.color} ${category.color === 'bg-yellow-400' ? 'text-black' : 'text-white'}`
                    : 'bg-white text-black hover:bg-gray-100'
                }`}
              >
                {category.name}
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* Gallery Grid */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 auto-rows-max">
            {filteredImages.map((image, index) => (
              <div
                key={image.id}
                className={`group cursor-pointer ${getSizeClasses(image.size)} ${
                  index % 3 === 0 ? 'asymmetric-grid' : index % 3 === 1 ? 'anti-asymmetric' : ''
                }`}
                onClick={() => openImageModal(image)}
              >
                <Card className="h-full overflow-hidden p-0 group-hover:translate-x-2 group-hover:translate-y-2 group-hover:shadow-none transition-all duration-300">
                  <div className="relative overflow-hidden flex-1">
                    {/* Loading skeleton */}
                    {!imagesLoaded[image.id] && (
                      <div className="absolute inset-0 bg-gray-200 animate-pulse flex items-center justify-center">
                        <div className="brutal-text text-gray-400">LOADING...</div>
                      </div>
                    )}
                    
                    <img
                      src={image.src}
                      alt={image.alt}
                      className={`w-full h-full object-cover transition-all duration-300 group-hover:scale-105 ${
                        imagesLoaded[image.id] ? 'opacity-100' : 'opacity-0'
                      }`}
                      onLoad={() => handleImageLoad(image.id)}
                      loading="lazy"
                    />
                    
                    {/* Overlay */}
                    <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-70 transition-all duration-300 flex items-center justify-center">
                      <div className="opacity-0 group-hover:opacity-100 transition-opacity duration-300 text-center">
                        <Eye className="w-8 h-8 text-white mx-auto mb-2" />
                        <p className="brutal-text text-white text-sm">VIEW PROJECT</p>
                      </div>
                    </div>
                  </div>
                  
                  <div className="p-4">
                    <h3 className="brutal-text text-lg mb-2">{image.title}</h3>
                    <p className="font-bold text-sm text-gray-600 mb-2">{image.description}</p>
                    <span className={`inline-block px-3 py-1 brutal-text text-xs ${
                      galleryCategories.find(cat => cat.id === image.category)?.color || 'bg-gray-500'
                    } ${image.category === 'design' ? 'text-black' : 'text-white'} brutal-border`}>
                      {image.category.toUpperCase()}
                    </span>
                  </div>
                </Card>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Image Modal */}
      {selectedImage && (
        <div className="fixed inset-0 bg-black bg-opacity-80 flex items-center justify-center z-50 p-4">
          <div className="bg-white brutal-border brutal-shadow max-w-4xl w-full max-h-[90vh] overflow-auto">
            <div className="p-6">
              <div className="flex justify-between items-start mb-4">
                <div>
                  <h3 className="brutal-text text-2xl mb-2">{selectedImage.title}</h3>
                  <p className="font-bold text-gray-600">{selectedImage.description}</p>
                </div>
                <button
                  onClick={closeImageModal}
                  className="bg-red-500 text-white p-2 brutal-border brutal-shadow hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150"
                >
                  ✕
                </button>
              </div>
              
              <div className="mb-4">
                <img
                  src={selectedImage.src}
                  alt={selectedImage.alt}
                  className="w-full h-auto brutal-border"
                />
              </div>
              
              <div className="flex justify-between items-center">
                <span className={`px-4 py-2 brutal-text text-sm ${
                  galleryCategories.find(cat => cat.id === selectedImage.category)?.color || 'bg-gray-500'
                } ${selectedImage.category === 'design' ? 'text-black' : 'text-white'} brutal-border`}>
                  {selectedImage.category.toUpperCase()}
                </span>
                
                <button
                  onClick={closeImageModal}
                  className="bg-blue-500 text-white px-6 py-3 brutal-border brutal-shadow brutal-text text-sm hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150 inline-flex items-center gap-2"
                >
                  <ExternalLink className="w-4 h-4" />
                  CLOSE
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
