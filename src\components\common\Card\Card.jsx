import React from 'react';

const Card = ({ 
  children, 
  variant = 'default', 
  className = '', 
  asymmetric = false,
  ...props 
}) => {
  const baseClasses = 'brutal-border brutal-shadow p-6';
  
  const variants = {
    default: 'bg-white',
    primary: 'bg-blue-500 text-white',
    secondary: 'bg-yellow-400 text-black',
    accent: 'bg-pink-500 text-white',
    success: 'bg-green-400 text-black',
    dark: 'bg-black text-white'
  };

  const asymmetricClass = asymmetric ? (Math.random() > 0.5 ? 'asymmetric-grid' : 'anti-asymmetric') : '';

  const classes = `${baseClasses} ${variants[variant]} ${asymmetricClass} ${className}`;

  return (
    <div className={classes} {...props}>
      {children}
    </div>
  );
};

export default Card;
