export const caseStudies = [
  {
    "client_name": "TechFlow Solutions",
    "client_logo_url": null,
    "industry": "SaaS",
    "challenge": "B2B SaaS company struggling to generate qualified leads through social media. They were getting lots of engagement but zero conversions to their demo requests.",
    "solution": "We implemented a LinkedIn-focused content strategy with targeted lead magnets, created conversion-optimized Facebook campaigns, and built automated nurture sequences that guide prospects from social media to sales calls.",
    "results": [
      {
        "metric": "Demo Requests",
        "value": "340%",
        "improvement": "increase in 90 days"
      },
      {
        "metric": "Cost per Lead",
        "value": "67%",
        "improvement": "reduction"
      },
      {
        "metric": "Revenue Attribution",
        "value": "$180K",
        "improvement": "from social media"
      },
      {
        "metric": "ROAS",
        "value": "4.2x",
        "improvement": "return on ad spend"
      }
    ],
    "featured_image_url": null,
    "testimonial": "On-GeneralServices completely transformed our social media from a cost center into our #1 lead generation channel. The results speak for themselves.",
    "testimonial_author": "<PERSON>, CMO at TechFlow Solutions",
    "services_used": [
      "Content Creation",
      "Paid Advertising",
      "Analytics & Reporting"
    ],
    "id": "6896266c6de4794031eaceab",
    "created_date": "2025-08-08T16:31:40.678000",
    "updated_date": "2025-08-08T16:31:40.678000",
    "created_by_id": "6896252c2d75e7f0965c03ed",
    "created_by": "<EMAIL>",
    "is_sample": false
  },
  {
    "client_name": "FitLife Gym Chain",
    "client_logo_url": null,
    "industry": "Fitness",
    "challenge": "Local gym chain wanted to compete with big fitness franchises and fill their new locations with members. They had great facilities but zero social media presence.",
    "solution": "Built a community-driven social media strategy showcasing member transformations, created location-specific Facebook ads targeting people within 5 miles of each gym, and implemented a referral system that incentivizes social sharing.",
    "results": [
      {
        "metric": "New Memberships",
        "value": "850",
        "improvement": "in 6 months"
      },
      {
        "metric": "Social Media Revenue",
        "value": "$127K",
        "improvement": "directly attributed"
      },
      {
        "metric": "Member Retention",
        "value": "34%",
        "improvement": "increase"
      },
      {
        "metric": "Cost per Acquisition",
        "value": "$23",
        "improvement": "vs industry avg of $87"
      }
    ],
    "featured_image_url": null,
    "testimonial": "We went from ghost town to packed gym in 6 months. Our social media actually brings in more members than traditional advertising now.",
    "testimonial_author": "Mike Rodriguez, Owner of FitLife Gym Chain",
    "services_used": [
      "Community Management",
      "Paid Advertising",
      "Content Creation"
    ],
    "id": "6896266c6de4794031eaceac",
    "created_date": "2025-08-08T16:31:40.678000",
    "updated_date": "2025-08-08T16:31:40.678000",
    "created_by_id": "6896252c2d75e7f0965c03ed",
    "created_by": "<EMAIL>",
    "is_sample": false
  },
  {
    "client_name": "Luxe Beauty Co",
    "client_logo_url": null,
    "industry": "Beauty & Cosmetics",
    "challenge": "Premium beauty brand launching new product line but struggling to build brand awareness and drive e-commerce sales in a saturated market.",
    "solution": "Developed an influencer-partnership strategy combined with user-generated content campaigns, created Instagram Shopping integration, and ran conversion-focused campaigns targeting beauty enthusiasts with high purchase intent.",
    "results": [
      {
        "metric": "Online Sales",
        "value": "290%",
        "improvement": "increase"
      },
      {
        "metric": "Brand Mentions",
        "value": "500%",
        "improvement": "increase"
      },
      {
        "metric": "Email Signups",
        "value": "1,200",
        "improvement": "new subscribers"
      },
      {
        "metric": "ROAS",
        "value": "5.8x",
        "improvement": "return on ad spend"
      }
    ],
    "featured_image_url": null,
    "testimonial": "On-GeneralServices didn't just get us sales, they built us a brand. Our social media presence now rivals companies 10x our size.",
    "testimonial_author": "Jessica Park, Founder of Luxe Beauty Co",
    "services_used": [
      "Content Creation",
      "Paid Advertising",
      "Community Management"
    ],
    "id": "6896266c6de4794031eacead",
    "created_date": "2025-08-08T16:31:40.678000",
    "updated_date": "2025-08-08T16:31:40.678000",
    "created_by_id": "6896252c2d75e7f0965c03ed",
    "created_by": "<EMAIL>",
    "is_sample": false
  }
];
