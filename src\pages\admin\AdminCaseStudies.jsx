import React, { useState } from 'react';
import { Plus, Edit, Trash2, Eye, TrendingUp, Award, DollarSign } from 'lucide-react';
import DataTable from '../../components/admin/DataTable';
import Modal from '../../components/admin/Modal';
import FormField from '../../components/admin/FormField';
import ActionButton from '../../components/admin/ActionButton';
import { caseStudies } from '../../data/caseStudies';

const AdminCaseStudies = () => {
  const [studies, setStudies] = useState(caseStudies);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingStudy, setEditingStudy] = useState(null);
  const [formData, setFormData] = useState({
    client_name: '',
    industry: '',
    challenge: '',
    solution: '',
    testimonial: '',
    testimonial_author: '',
    services_used: [],
    results: []
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleServicesChange = (e) => {
    const services = e.target.value.split(',').map(s => s.trim()).filter(s => s);
    setFormData(prev => ({
      ...prev,
      services_used: services
    }));
  };

  const handleResultChange = (index, field, value) => {
    setFormData(prev => ({
      ...prev,
      results: prev.results.map((result, i) => 
        i === index ? { ...result, [field]: value } : result
      )
    }));
  };

  const addResult = () => {
    setFormData(prev => ({
      ...prev,
      results: [...prev.results, { metric: '', value: '', improvement: '' }]
    }));
  };

  const removeResult = (index) => {
    setFormData(prev => ({
      ...prev,
      results: prev.results.filter((_, i) => i !== index)
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (editingStudy) {
      // Update existing case study
      setStudies(prev => prev.map(study => 
        study.id === editingStudy.id 
          ? { 
              ...study, 
              ...formData,
              updated_date: new Date().toISOString()
            }
          : study
      ));
    } else {
      // Add new case study
      const newStudy = {
        id: Date.now().toString(),
        ...formData,
        client_logo_url: null,
        featured_image_url: null,
        created_date: new Date().toISOString(),
        updated_date: new Date().toISOString(),
        created_by_id: 'admin',
        created_by: '<EMAIL>',
        is_sample: false
      };
      setStudies(prev => [...prev, newStudy]);
    }
    
    handleCloseModal();
  };

  const handleEdit = (study) => {
    setEditingStudy(study);
    setFormData({
      client_name: study.client_name,
      industry: study.industry,
      challenge: study.challenge,
      solution: study.solution,
      testimonial: study.testimonial,
      testimonial_author: study.testimonial_author,
      services_used: study.services_used,
      results: study.results
    });
    setIsModalOpen(true);
  };

  const handleDelete = (study) => {
    if (window.confirm(`Are you sure you want to delete the case study for "${study.client_name}"?`)) {
      setStudies(prev => prev.filter(s => s.id !== study.id));
    }
  };

  const handleView = (study) => {
    // For now, just open edit modal in view mode
    handleEdit(study);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setEditingStudy(null);
    setFormData({
      client_name: '',
      industry: '',
      challenge: '',
      solution: '',
      testimonial: '',
      testimonial_author: '',
      services_used: [],
      results: []
    });
  };

  const handleAddNew = () => {
    setEditingStudy(null);
    setFormData({
      client_name: '',
      industry: '',
      challenge: '',
      solution: '',
      testimonial: '',
      testimonial_author: '',
      services_used: [],
      results: [{ metric: '', value: '', improvement: '' }]
    });
    setIsModalOpen(true);
  };

  const columns = [
    {
      key: 'client_name',
      label: 'CLIENT',
      sortable: true,
      render: (name, study) => (
        <div>
          <div className="font-bold">{name}</div>
          <div className="text-sm text-gray-600">{study.industry}</div>
        </div>
      )
    },
    {
      key: 'services_used',
      label: 'SERVICES',
      render: (services) => (
        <div className="flex flex-wrap gap-1">
          {services.slice(0, 2).map((service, index) => (
            <span key={index} className="px-2 py-1 text-xs font-bold bg-blue-100 text-blue-800 brutal-border">
              {service}
            </span>
          ))}
          {services.length > 2 && (
            <span className="px-2 py-1 text-xs font-bold bg-gray-100 text-gray-800 brutal-border">
              +{services.length - 2}
            </span>
          )}
        </div>
      )
    },
    {
      key: 'results',
      label: 'KEY RESULTS',
      render: (results) => (
        <div className="space-y-1">
          {results.slice(0, 2).map((result, index) => (
            <div key={index} className="text-sm">
              <span className="font-bold text-green-600">{result.value}</span>
              <span className="text-gray-600 ml-1">{result.metric}</span>
            </div>
          ))}
          {results.length > 2 && (
            <div className="text-xs text-gray-500">+{results.length - 2} more</div>
          )}
        </div>
      )
    },
    {
      key: 'created_date',
      label: 'CREATED',
      sortable: true,
      render: (date) => (
        <span className="text-sm">
          {new Date(date).toLocaleDateString('en-GB')}
        </span>
      )
    }
  ];

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white brutal-border brutal-shadow p-6">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <div>
            <h1 className="brutal-text text-3xl mb-2">CASE STUDIES MANAGEMENT</h1>
            <p className="font-bold text-gray-600">
              Manage client success stories and project results
            </p>
          </div>
          
          <ActionButton 
            onClick={handleAddNew}
            icon={Plus}
            variant="primary"
          >
            ADD NEW CASE STUDY
          </ActionButton>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white brutal-border brutal-shadow p-6">
          <div className="flex items-center gap-4">
            <div className="bg-blue-500 text-white p-3 brutal-border brutal-shadow-small">
              <Award className="w-6 h-6" />
            </div>
            <div>
              <div className="brutal-text text-2xl">{studies.length}</div>
              <div className="font-bold text-gray-600 text-sm">TOTAL CASE STUDIES</div>
            </div>
          </div>
        </div>
        
        <div className="bg-white brutal-border brutal-shadow p-6">
          <div className="flex items-center gap-4">
            <div className="bg-green-500 text-white p-3 brutal-border brutal-shadow-small">
              <TrendingUp className="w-6 h-6" />
            </div>
            <div>
              <div className="brutal-text text-2xl">
                {studies.reduce((acc, study) => acc + study.results.length, 0)}
              </div>
              <div className="font-bold text-gray-600 text-sm">TOTAL RESULTS</div>
            </div>
          </div>
        </div>
        
        <div className="bg-white brutal-border brutal-shadow p-6">
          <div className="flex items-center gap-4">
            <div className="bg-yellow-500 text-black p-3 brutal-border brutal-shadow-small">
              <DollarSign className="w-6 h-6" />
            </div>
            <div>
              <div className="brutal-text text-2xl">
                {new Set(studies.map(s => s.industry)).size}
              </div>
              <div className="font-bold text-gray-600 text-sm">INDUSTRIES SERVED</div>
            </div>
          </div>
        </div>
      </div>

      {/* Data Table */}
      <DataTable
        data={studies}
        columns={columns}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onView={handleView}
        searchable={true}
        filterable={true}
      />

      {/* Add/Edit Modal */}
      <Modal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        title={editingStudy ? 'EDIT CASE STUDY' : 'ADD NEW CASE STUDY'}
        size="full"
      >
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              label="Client Name"
              name="client_name"
              value={formData.client_name}
              onChange={handleInputChange}
              placeholder="Enter client name"
              required
            />
            
            <FormField
              label="Industry"
              name="industry"
              value={formData.industry}
              onChange={handleInputChange}
              placeholder="Enter industry"
              required
            />
          </div>
          
          {/* Challenge & Solution */}
          <FormField
            label="Challenge"
            type="textarea"
            name="challenge"
            value={formData.challenge}
            onChange={handleInputChange}
            placeholder="Describe the client's challenge"
            rows={4}
            required
          />
          
          <FormField
            label="Solution"
            type="textarea"
            name="solution"
            value={formData.solution}
            onChange={handleInputChange}
            placeholder="Describe the solution provided"
            rows={4}
            required
          />
          
          {/* Services Used */}
          <FormField
            label="Services Used (comma-separated)"
            name="services_used"
            value={formData.services_used.join(', ')}
            onChange={handleServicesChange}
            placeholder="e.g., Content Creation, Paid Advertising, Analytics"
            required
          />
          
          {/* Results */}
          <div>
            <div className="flex items-center justify-between mb-4">
              <label className="block font-bold text-black">Results</label>
              <ActionButton
                type="button"
                onClick={addResult}
                variant="secondary"
                size="small"
                icon={Plus}
              >
                ADD RESULT
              </ActionButton>
            </div>
            
            <div className="space-y-4">
              {formData.results.map((result, index) => (
                <div key={index} className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-gray-50 brutal-border">
                  <FormField
                    label="Metric"
                    value={result.metric}
                    onChange={(e) => handleResultChange(index, 'metric', e.target.value)}
                    placeholder="e.g., Demo Requests"
                  />
                  
                  <FormField
                    label="Value"
                    value={result.value}
                    onChange={(e) => handleResultChange(index, 'value', e.target.value)}
                    placeholder="e.g., 340%"
                  />
                  
                  <div className="flex gap-2">
                    <FormField
                      label="Improvement"
                      value={result.improvement}
                      onChange={(e) => handleResultChange(index, 'improvement', e.target.value)}
                      placeholder="e.g., increase in 90 days"
                      className="flex-1"
                    />
                    
                    <ActionButton
                      type="button"
                      onClick={() => removeResult(index)}
                      variant="danger"
                      size="small"
                      icon={Trash2}
                      className="mt-6"
                    >
                      REMOVE
                    </ActionButton>
                  </div>
                </div>
              ))}
            </div>
          </div>
          
          {/* Testimonial */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              label="Testimonial"
              type="textarea"
              name="testimonial"
              value={formData.testimonial}
              onChange={handleInputChange}
              placeholder="Client testimonial"
              rows={4}
            />
            
            <FormField
              label="Testimonial Author"
              name="testimonial_author"
              value={formData.testimonial_author}
              onChange={handleInputChange}
              placeholder="e.g., John Doe, CEO at Company"
            />
          </div>
          
          <div className="flex gap-4 pt-4">
            <ActionButton
              type="submit"
              variant="primary"
              icon={editingStudy ? Edit : Plus}
            >
              {editingStudy ? 'UPDATE CASE STUDY' : 'ADD CASE STUDY'}
            </ActionButton>
            
            <ActionButton
              type="button"
              variant="outline"
              onClick={handleCloseModal}
            >
              CANCEL
            </ActionButton>
          </div>
        </form>
      </Modal>
    </div>
  );
};

export default AdminCaseStudies;
