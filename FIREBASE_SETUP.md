# Firebase Setup Instructions for on-generalservices

This guide will help you set up Firebase for your on-generalservices web application.

## Prerequisites

- A Google account
- Node.js and npm installed
- The project running locally

## Step 1: Create a Firebase Project

1. Go to the [Firebase Console](https://console.firebase.google.com)
2. Click "Create a project" or "Add project"
3. Enter project name: `on-generalservices` (or your preferred name)
4. Choose whether to enable Google Analytics (recommended)
5. Select or create a Google Analytics account if enabled
6. Click "Create project"

## Step 2: Enable Required Services

### Authentication
1. In the Firebase Console, go to "Authentication"
2. Click "Get started"
3. Go to the "Sign-in method" tab
4. Enable "Email/Password" provider
5. Click "Save"

### Firestore Database
1. Go to "Firestore Database"
2. Click "Create database"
3. Choose "Start in test mode" (we'll update rules later)
4. Select a location closest to your users (e.g., us-central1)
5. Click "Done"

### Storage
1. Go to "Storage"
2. Click "Get started"
3. Choose "Start in test mode"
4. Select the same location as your Firestore database
5. Click "Done"

## Step 3: Get Firebase Configuration

1. In the Firebase Console, click the gear icon (⚙️) next to "Project Overview"
2. Select "Project settings"
3. Scroll down to "Your apps" section
4. Click the web icon (`</>`) to add a web app
5. Enter app nickname: `on-generalservices-web`
6. Check "Also set up Firebase Hosting" (optional)
7. Click "Register app"
8. Copy the configuration object

## Step 4: Configure Environment Variables

1. Open the `.env` file in your project root
2. Replace the placeholder values with your Firebase configuration:

```env
VITE_FIREBASE_API_KEY=your_api_key_here
VITE_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_project_id.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your_messaging_sender_id
VITE_FIREBASE_APP_ID=your_app_id
VITE_FIREBASE_MEASUREMENT_ID=your_measurement_id
```

## Step 5: Set Up Security Rules

### Firestore Rules
1. In Firebase Console, go to "Firestore Database"
2. Click on the "Rules" tab
3. Replace the default rules with the content from `firestore.rules` file
4. Click "Publish"

### Storage Rules
1. In Firebase Console, go to "Storage"
2. Click on the "Rules" tab
3. Replace the default rules with the content from `storage.rules` file
4. Click "Publish"

## Step 6: Create Admin User

1. In Firebase Console, go to "Authentication"
2. Click on the "Users" tab
3. Click "Add user"
4. Enter email: `<EMAIL>` (or your preferred admin email)
5. Enter a secure password
6. Click "Add user"
7. Note the User UID for the next step

## Step 7: Create Admin User Document

1. In Firebase Console, go to "Firestore Database"
2. Click "Start collection"
3. Collection ID: `users`
4. Document ID: Use the User UID from step 6
5. Add the following fields:
   - `email` (string): `<EMAIL>`
   - `displayName` (string): `Admin User`
   - `role` (string): `admin`
   - `createdAt` (timestamp): Current timestamp
   - `isActive` (boolean): `true`
6. Click "Save"

## Step 8: Test the Setup

1. Start your development server: `npm run dev`
2. Navigate to `/admin/login`
3. Try logging in with your admin credentials
4. Go to `/admin` to access the dashboard
5. Use the Firebase Setup component to migrate dummy data

## Step 9: Deploy Security Rules (Production)

When ready for production:

1. Install Firebase CLI: `npm install -g firebase-tools`
2. Login to Firebase: `firebase login`
3. Initialize Firebase in your project: `firebase init`
   - Select Firestore, Storage, and Hosting
   - Use existing project
   - Accept default file names
4. Deploy rules: `firebase deploy --only firestore:rules,storage`

## Troubleshooting

### Common Issues

1. **"Firebase not configured" error**
   - Check that all environment variables are set correctly
   - Restart the development server after changing .env

2. **Authentication errors**
   - Verify the admin user exists in Firebase Authentication
   - Check that the user document exists in Firestore with role: 'admin'

3. **Permission denied errors**
   - Ensure security rules are deployed correctly
   - Check that the user has the correct role in Firestore

4. **CORS errors**
   - Make sure your domain is added to Firebase Authentication settings
   - Check that the Firebase project is correctly configured

### Support

If you encounter issues:
1. Check the browser console for detailed error messages
2. Verify Firebase project settings
3. Ensure all services are enabled in Firebase Console
4. Check that security rules are properly deployed

## Next Steps

After Firebase is set up:
1. Use the admin panel to manage content
2. Test file uploads in the gallery section
3. Submit contact forms to test database integration
4. Monitor usage in Firebase Console
5. Set up Firebase Analytics for insights

## Security Considerations

- Never commit the `.env` file to version control
- Regularly review Firebase security rules
- Monitor Firebase usage and costs
- Set up Firebase security alerts
- Use strong passwords for admin accounts
- Consider enabling 2FA for Firebase Console access
