// Dummy contact messages data for admin panel
export const contactMessages = [
  {
    id: 1,
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+*********** 456',
    company: 'Tech Innovators Ltd',
    subject: 'Mobile App Development Inquiry',
    message: 'Hello, we are looking for a team to develop a mobile application for our business. We need both iOS and Android versions with user authentication, payment integration, and real-time notifications. Could you please provide a quote and timeline?',
    status: 'new',
    priority: 'high',
    created_date: '2025-01-06T10:30:00.000Z',
    updated_date: '2025-01-06T10:30:00.000Z',
    assigned_to: null,
    notes: []
  },
  {
    id: 2,
    name: '<PERSON>',
    email: '<EMAIL>',
    phone: '+*********** 654',
    company: 'Kimaro Enterprises',
    subject: 'Website Redesign Project',
    message: 'Our current website needs a complete redesign. We want a modern, responsive design that showcases our services better. We also need SEO optimization and content management system integration.',
    status: 'in_progress',
    priority: 'medium',
    created_date: '2025-01-05T14:15:00.000Z',
    updated_date: '2025-01-06T09:20:00.000Z',
    assigned_to: 'Admin User',
    notes: [
      {
        id: 1,
        note: 'Initial consultation scheduled for tomorrow',
        created_by: 'Admin User',
        created_date: '2025-01-06T09:20:00.000Z'
      }
    ]
  },
  {
    id: 3,
    name: 'Maria Mwanga',
    email: '<EMAIL>',
    phone: '+*********** 109',
    company: 'Mwanga Music Studio',
    subject: 'Music Video Production',
    message: 'I need professional music video production services for my upcoming album. Looking for creative direction, filming, and post-production editing. Please let me know your packages and pricing.',
    status: 'completed',
    priority: 'medium',
    created_date: '2025-01-04T16:45:00.000Z',
    updated_date: '2025-01-05T11:30:00.000Z',
    assigned_to: 'Admin User',
    notes: [
      {
        id: 1,
        note: 'Project completed successfully. Client very satisfied.',
        created_by: 'Admin User',
        created_date: '2025-01-05T11:30:00.000Z'
      }
    ]
  },
  {
    id: 4,
    name: 'David Mwalimu',
    email: '<EMAIL>',
    phone: '+*********** 678',
    company: 'Personal',
    subject: 'Wedding Photography Services',
    message: 'Hi, I am getting married in March and would like to book your photography services. We need full day coverage, engagement photos, and a wedding album. What packages do you offer?',
    status: 'new',
    priority: 'low',
    created_date: '2025-01-06T08:20:00.000Z',
    updated_date: '2025-01-06T08:20:00.000Z',
    assigned_to: null,
    notes: []
  },
  {
    id: 5,
    name: 'Grace Kimaro',
    email: '<EMAIL>',
    phone: '+*********** 321',
    company: 'Dodoma Business Solutions',
    subject: 'Digital Marketing Campaign',
    message: 'We want to launch a comprehensive digital marketing campaign for our new product line. This includes social media marketing, Google Ads, content creation, and analytics. Can you help us with this?',
    status: 'in_progress',
    priority: 'high',
    created_date: '2025-01-03T13:10:00.000Z',
    updated_date: '2025-01-05T16:45:00.000Z',
    assigned_to: 'Admin User',
    notes: [
      {
        id: 1,
        note: 'Strategy meeting completed. Proposal sent.',
        created_by: 'Admin User',
        created_date: '2025-01-05T16:45:00.000Z'
      }
    ]
  },
  {
    id: 6,
    name: 'Peter Mwanga',
    email: '<EMAIL>',
    phone: '+*********** 012',
    company: 'Iringa Events',
    subject: 'Event Documentation Services',
    message: 'We organize corporate events and need professional documentation services including photography, videography, and live streaming. Do you provide these services for events in Iringa?',
    status: 'new',
    priority: 'medium',
    created_date: '2025-01-06T07:30:00.000Z',
    updated_date: '2025-01-06T07:30:00.000Z',
    assigned_to: null,
    notes: []
  },
  {
    id: 7,
    name: 'Amina Hassan',
    email: '<EMAIL>',
    phone: '+*********** 890',
    company: 'TZ Startup Hub',
    subject: 'Brand Identity Design',
    message: 'Our startup needs complete brand identity design including logo, business cards, letterheads, and brand guidelines. We are looking for a modern, professional design that represents innovation.',
    status: 'completed',
    priority: 'medium',
    created_date: '2025-01-02T11:20:00.000Z',
    updated_date: '2025-01-04T14:15:00.000Z',
    assigned_to: 'Admin User',
    notes: [
      {
        id: 1,
        note: 'Brand identity package delivered. Client approved all designs.',
        created_by: 'Admin User',
        created_date: '2025-01-04T14:15:00.000Z'
      }
    ]
  }
];

export const messageStatuses = [
  { value: 'new', label: 'New', color: 'bg-blue-500' },
  { value: 'in_progress', label: 'In Progress', color: 'bg-yellow-500' },
  { value: 'completed', label: 'Completed', color: 'bg-green-500' },
  { value: 'cancelled', label: 'Cancelled', color: 'bg-red-500' }
];

export const messagePriorities = [
  { value: 'low', label: 'Low', color: 'bg-gray-500' },
  { value: 'medium', label: 'Medium', color: 'bg-yellow-500' },
  { value: 'high', label: 'High', color: 'bg-red-500' }
];
