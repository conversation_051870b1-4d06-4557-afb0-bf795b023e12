import React from "react";
import { Shield, Lock, Eye, FileText, Users, Mail } from "lucide-react";

export default function PrivacyPolicy() {
  const sections = [
    {
      icon: Shield,
      title: "INTRODUCTION",
      content: [
        "On-GeneralServices ('we,' 'our,' or 'us') is committed to protecting and respecting your privacy. This Privacy Policy explains how we collect, use, and protect your information when you visit our website or use our services.",
        "By using our website and services, you agree to the collection and use of information in accordance with this policy."
      ]
    },
    {
      icon: Eye,
      title: "INFORMATION WE COLLECT",
      content: [
        "Personal Information: Name, email address, phone number, company name, and other contact details you provide when filling out forms or contacting us.",
        "Usage Data: Information about how you use our website, including IP address, browser type, pages visited, and time spent on our site.",
        "Cookies and Tracking: We use cookies and similar technologies to enhance your experience and analyze website traffic."
      ]
    },
    {
      icon: FileText,
      title: "HOW WE USE YOUR INFORMATION",
      content: [
        "To provide and maintain our services",
        "To communicate with you about our services, including responding to inquiries and sending marketing materials",
        "To improve our website and services based on your feedback and usage patterns",
        "To comply with legal obligations and protect our rights"
      ]
    },
    {
      icon: Lock,
      title: "DATA PROTECTION",
      content: [
        "We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction.",
        "Your data is stored on secure servers and is only accessible by authorized personnel who need it to perform their job functions.",
        "We do not sell, trade, or rent your personal information to third parties."
      ]
    },
    {
      icon: Users,
      title: "SHARING YOUR INFORMATION",
      content: [
        "Service Providers: We may share your information with trusted third-party service providers who help us operate our business (e.g., email marketing platforms, analytics tools).",
        "Legal Requirements: We may disclose your information if required by law or to protect our rights and safety.",
        "Business Transfers: In the event of a merger or acquisition, your information may be transferred to the new entity."
      ]
    },
    {
      icon: Mail,
      title: "YOUR RIGHTS",
      content: [
        "Access: You have the right to request access to the personal information we hold about you.",
        "Correction: You can request that we correct any inaccurate or incomplete information.",
        "Deletion: You can request that we delete your personal information, subject to certain legal obligations.",
        "Opt-out: You can unsubscribe from our marketing communications at any time."
      ]
    }
  ];

  return (
    <div className="bg-white min-h-screen">
      {/* Header */}
      <section className="py-20 bg-blue-500">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-white text-black p-8 brutal-border brutal-shadow inline-block transform rotate-1 mb-8">
            <h1 className="brutal-text text-4xl md:text-6xl">PRIVACY POLICY</h1>
          </div>
          <p className="text-xl font-bold text-white max-w-2xl mx-auto">
            We respect your privacy and are committed to protecting your personal data. 
            Here's exactly how we collect, use, and protect your information.
          </p>
        </div>
      </section>

      {/* Last Updated */}
      <section className="py-8 bg-yellow-400">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-black text-white px-6 py-3 brutal-border brutal-shadow inline-block">
            <p className="brutal-text">LAST UPDATED: DECEMBER 2024</p>
          </div>
        </div>
      </section>

      {/* Content */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-12">
            {sections.map((section, index) => (
              <div key={index} className={`bg-white brutal-border brutal-shadow p-8 ${index % 2 === 0 ? 'asymmetric-grid' : 'anti-asymmetric'}`}>
                <div className="flex items-center gap-4 mb-6">
                  <div className="bg-green-400 w-12 h-12 brutal-border brutal-shadow flex items-center justify-center">
                    <section.icon className="w-6 h-6 text-black" />
                  </div>
                  <h2 className="brutal-text text-3xl">{section.title}</h2>
                </div>
                <div className="font-bold text-gray-800 space-y-4">
                  {section.content.map((paragraph, pIndex) => (
                    <p key={pIndex}>{paragraph}</p>
                  ))}
                </div>
              </div>
            ))}

            {/* Contact Information */}
            <div className="bg-pink-500 text-white p-8 brutal-border brutal-shadow text-center">
              <h2 className="brutal-text text-3xl mb-6">QUESTIONS ABOUT THIS POLICY?</h2>
              <p className="text-xl font-bold mb-6">
                If you have any questions about this Privacy Policy or how we handle your data, please contact us:
              </p>
              <div className="space-y-2 font-bold">
                <p>Email: <EMAIL></p>
                <p>Phone: +255 748 808 044</p>
                <p>Address: 41220 Nzuguni, Dodoma, Tanzania</p>
              </div>
            </div>

            {/* Updates */}
            <div className="bg-gray-100 brutal-border p-8 text-center">
              <h3 className="brutal-text text-2xl mb-4">POLICY UPDATES</h3>
              <p className="font-bold text-gray-800">
                We may update this Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page and updating the "Last Updated" date. 
                We encourage you to review this Privacy Policy periodically for any changes.
              </p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
