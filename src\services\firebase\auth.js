// Firebase Authentication Service
import { 
  signInWithEmailAndPassword,
  signOut,
  onAuthStateChanged,
  createUserWithEmailAndPassword,
  updateProfile,
  sendPasswordResetEmail,
  updatePassword,
  EmailAuthProvider,
  reauthenticateWithCredential
} from 'firebase/auth';
import { doc, setDoc, getDoc, updateDoc, serverTimestamp } from 'firebase/firestore';
import { auth, db, COLLECTIONS } from '../../config/firebase';

/**
 * Authentication service for Firebase
 */
export class AuthService {
  /**
   * Sign in with email and password
   * @param {string} email 
   * @param {string} password 
   * @returns {Promise<{success: boolean, user?: object, error?: string}>}
   */
  static async signIn(email, password) {
    try {
      const userCredential = await signInWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;
      
      // Update last login time
      await this.updateUserLastLogin(user.uid);
      
      // Get user profile data
      const userProfile = await this.getUserProfile(user.uid);
      
      return {
        success: true,
        user: {
          uid: user.uid,
          email: user.email,
          displayName: user.displayName,
          ...userProfile
        }
      };
    } catch (error) {
      console.error('Sign in error:', error);
      return {
        success: false,
        error: this.getAuthErrorMessage(error.code)
      };
    }
  }

  /**
   * Sign out current user
   * @returns {Promise<{success: boolean, error?: string}>}
   */
  static async signOut() {
    try {
      await signOut(auth);
      return { success: true };
    } catch (error) {
      console.error('Sign out error:', error);
      return {
        success: false,
        error: 'Failed to sign out. Please try again.'
      };
    }
  }

  /**
   * Create a new user account (admin only)
   * @param {object} userData 
   * @returns {Promise<{success: boolean, user?: object, error?: string}>}
   */
  static async createUser(userData) {
    try {
      const { email, password, displayName, role = 'admin' } = userData;
      
      const userCredential = await createUserWithEmailAndPassword(auth, email, password);
      const user = userCredential.user;
      
      // Update user profile
      await updateProfile(user, { displayName });
      
      // Create user document in Firestore
      await setDoc(doc(db, COLLECTIONS.USERS, user.uid), {
        email: user.email,
        displayName: displayName,
        role: role,
        createdAt: serverTimestamp(),
        lastLogin: serverTimestamp(),
        isActive: true
      });
      
      return {
        success: true,
        user: {
          uid: user.uid,
          email: user.email,
          displayName: displayName,
          role: role
        }
      };
    } catch (error) {
      console.error('Create user error:', error);
      return {
        success: false,
        error: this.getAuthErrorMessage(error.code)
      };
    }
  }

  /**
   * Get user profile from Firestore
   * @param {string} uid 
   * @returns {Promise<object|null>}
   */
  static async getUserProfile(uid) {
    try {
      const userDoc = await getDoc(doc(db, COLLECTIONS.USERS, uid));
      if (userDoc.exists()) {
        return userDoc.data();
      }
      return null;
    } catch (error) {
      console.error('Get user profile error:', error);
      return null;
    }
  }

  /**
   * Update user's last login time
   * @param {string} uid 
   */
  static async updateUserLastLogin(uid) {
    try {
      await updateDoc(doc(db, COLLECTIONS.USERS, uid), {
        lastLogin: serverTimestamp()
      });
    } catch (error) {
      console.error('Update last login error:', error);
    }
  }

  /**
   * Send password reset email
   * @param {string} email 
   * @returns {Promise<{success: boolean, error?: string}>}
   */
  static async sendPasswordReset(email) {
    try {
      await sendPasswordResetEmail(auth, email);
      return { success: true };
    } catch (error) {
      console.error('Password reset error:', error);
      return {
        success: false,
        error: this.getAuthErrorMessage(error.code)
      };
    }
  }

  /**
   * Update user password
   * @param {string} currentPassword 
   * @param {string} newPassword 
   * @returns {Promise<{success: boolean, error?: string}>}
   */
  static async updateUserPassword(currentPassword, newPassword) {
    try {
      const user = auth.currentUser;
      if (!user) {
        throw new Error('No authenticated user');
      }

      // Re-authenticate user
      const credential = EmailAuthProvider.credential(user.email, currentPassword);
      await reauthenticateWithCredential(user, credential);
      
      // Update password
      await updatePassword(user, newPassword);
      
      return { success: true };
    } catch (error) {
      console.error('Update password error:', error);
      return {
        success: false,
        error: this.getAuthErrorMessage(error.code)
      };
    }
  }

  /**
   * Listen to authentication state changes
   * @param {function} callback 
   * @returns {function} Unsubscribe function
   */
  static onAuthStateChanged(callback) {
    return onAuthStateChanged(auth, async (user) => {
      if (user) {
        const userProfile = await this.getUserProfile(user.uid);
        callback({
          uid: user.uid,
          email: user.email,
          displayName: user.displayName,
          ...userProfile
        });
      } else {
        callback(null);
      }
    });
  }

  /**
   * Get current authenticated user
   * @returns {object|null}
   */
  static getCurrentUser() {
    return auth.currentUser;
  }

  /**
   * Convert Firebase auth error codes to user-friendly messages
   * @param {string} errorCode 
   * @returns {string}
   */
  static getAuthErrorMessage(errorCode) {
    const errorMessages = {
      'auth/user-not-found': 'No account found with this email address.',
      'auth/wrong-password': 'Incorrect password. Please try again.',
      'auth/invalid-email': 'Invalid email address format.',
      'auth/user-disabled': 'This account has been disabled.',
      'auth/too-many-requests': 'Too many failed attempts. Please try again later.',
      'auth/email-already-in-use': 'An account with this email already exists.',
      'auth/weak-password': 'Password should be at least 6 characters long.',
      'auth/requires-recent-login': 'Please sign in again to complete this action.',
      'auth/invalid-credential': 'Invalid credentials provided.'
    };

    return errorMessages[errorCode] || 'An unexpected error occurred. Please try again.';
  }
}

export default AuthService;
