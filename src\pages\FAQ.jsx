import React, { useState } from "react";
import { ChevronDown, ChevronUp, HelpCircle, MessageCircle, Clock, DollarSign, Users, Target, ArrowUpRight } from "lucide-react";
import { Link } from "react-router-dom";
import { createPageUrl } from "@/utils";

export default function FAQ() {
  const [openFaq, setOpenFaq] = useState(null);

  const faqCategories = [
    {
      category: "GENERAL QUESTIONS",
      icon: HelpCircle,
      color: "bg-blue-500",
      faqs: [
        {
          question: "What makes OnGeneral different from other technology service providers?",
          answer: "OnGeneral offers comprehensive technology solutions under one roof. From software development to media production, graphic design, and large format printing, we provide integrated services that work together seamlessly. Our team combines technical expertise with creative innovation to deliver solutions that drive real business growth."
        },
        {
          question: "How quickly can I expect to see results?",
          answer: "Project timelines vary based on scope and complexity. Simple graphic design projects may be completed within 1-2 weeks, while custom software development can take 2-6 months. We provide detailed timelines during our initial consultation and keep you updated throughout the process."
        },
        {
          question: "Do you work with businesses in all industries?",
          answer: "Yes, we work with businesses across various industries including healthcare, education, retail, manufacturing, hospitality, and professional services. Our diverse skill set allows us to adapt our solutions to meet the unique needs of different sectors."
        },
        {
          question: "What services does OnGeneral offer?",
          answer: "We offer software development (mobile and web applications), media production (video, photography, music recording), graphic design, social media marketing, large format printing, and voice acting services. We also provide full-service packages that combine multiple services for comprehensive solutions."
        }
      ]
    },
    {
      category: "PRICING & PACKAGES",
      icon: DollarSign,
      color: "bg-green-400",
      faqs: [
        {
          question: "How do you determine pricing for projects?",
          answer: "Our pricing is based on project scope, complexity, timeline, and required resources. We provide detailed quotes after understanding your specific requirements. For ongoing services like social media management, we offer monthly packages. All pricing is transparent with no hidden fees."
        },
        {
          question: "Do you offer payment plans?",
          answer: "Yes, for larger projects we offer flexible payment plans. Typically, we require 50% upfront and the remainder upon completion. For ongoing services, we offer monthly billing. We accept various payment methods including bank transfers and mobile money."
        },
        {
          question: "What's included in your service packages?",
          answer: "Our packages include initial consultation, project planning, development/creation, testing, deployment, training, and basic support. Specific inclusions vary by service type. We provide detailed breakdowns of what's included in each package during our consultation."
        },
        {
          question: "Do you offer any guarantees?",
          answer: "We guarantee professional service, timely delivery, and quality workmanship. If you're not satisfied with our work, we'll revise it until it meets your expectations. We also provide warranties on our software development projects and ongoing support for all our services."
        }
      ]
    },
    {
      category: "SERVICES & PROCESS",
      icon: Target,
      color: "bg-pink-500",
      faqs: [
        {
          question: "What does your project process look like?",
          answer: "We follow a 5-step process: Consultation & Analysis, Planning & Design, Development & Implementation, Testing & Deployment, and ongoing Support & Maintenance. Each step includes regular communication and progress updates to ensure your project stays on track."
        },
        {
          question: "How do you ensure quality in your work?",
          answer: "We follow strict quality assurance protocols including code reviews, testing phases, client feedback loops, and final quality checks. Our team uses industry best practices and the latest technologies to ensure high-quality deliverables."
        },
        {
          question: "Can I request changes during the project?",
          answer: "Yes, we understand that requirements may evolve. We accommodate reasonable changes during the project, though significant scope changes may affect timeline and cost. We discuss any changes with you before implementation."
        },
        {
          question: "Do you provide training for our team?",
          answer: "Absolutely. We provide comprehensive training for your team on how to use and maintain the solutions we develop. This includes user manuals, video tutorials, and hands-on training sessions as needed."
        }
      ]
    },
    {
      category: "TECHNICAL & SUPPORT",
      icon: Users,
      color: "bg-yellow-400",
      faqs: [
        {
          question: "What technical requirements do you need from us?",
          answer: "Requirements vary by project type. For software development, we may need access to existing systems, databases, or hosting environments. For media production, we may need brand assets or content materials. We'll discuss specific requirements during our consultation."
        },
        {
          question: "How do you handle data security and privacy?",
          answer: "We take data security seriously. We use secure development practices, encrypted communications, and follow industry standards for data protection. We can sign NDAs and comply with data protection regulations as required."
        },
        {
          question: "What kind of ongoing support do you provide?",
          answer: "We provide ongoing technical support, maintenance, updates, and troubleshooting for all our solutions. Support levels vary by service type, but we're always available to help ensure your technology solutions continue working effectively."
        },
        {
          question: "Do you work with existing systems and technologies?",
          answer: "Yes, we can integrate with existing systems, databases, and technologies. Our team has experience with various platforms and can work within your current technology ecosystem to ensure seamless integration."
        }
      ]
    }
  ];

  const toggleFaq = (categoryIndex, faqIndex) => {
    const key = `${categoryIndex}-${faqIndex}`;
    setOpenFaq(openFaq === key ? null : key);
  };

  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="py-20 bg-purple-500">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-white text-black p-8 brutal-border brutal-shadow inline-block transform -rotate-1 mb-8">
            <h1 className="brutal-text text-4xl md:text-6xl">FAQ</h1>
          </div>
          <p className="text-xl md:text-2xl font-bold text-white max-w-3xl mx-auto">
            Got questions? We've got answers. Everything you need to know about working with OnGeneral.
          </p>
        </div>
      </section>

      {/* FAQ Categories */}
      <section className="py-20">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="space-y-12">
            {faqCategories.map((category, categoryIndex) => (
              <div key={categoryIndex} className={categoryIndex % 2 === 0 ? "asymmetric-grid" : "anti-asymmetric"}>
                <div className="bg-white brutal-border brutal-shadow p-8">
                  {/* Category Header */}
                  <div className="flex items-center gap-4 mb-8">
                    <div className={`${category.color} text-white w-12 h-12 brutal-border brutal-shadow-small flex items-center justify-center`}>
                      <category.icon className="w-6 h-6" />
                    </div>
                    <h2 className="brutal-text text-2xl">{category.category}</h2>
                  </div>

                  {/* FAQ Items */}
                  <div className="space-y-4">
                    {category.faqs.map((faq, faqIndex) => {
                      const isOpen = openFaq === `${categoryIndex}-${faqIndex}`;
                      return (
                        <div key={faqIndex} className="brutal-border">
                          <button
                            onClick={() => toggleFaq(categoryIndex, faqIndex)}
                            className="w-full p-4 text-left bg-gray-100 hover:bg-gray-200 transition-colors duration-200 flex items-center justify-between"
                          >
                            <span className="font-bold text-lg pr-4">{faq.question}</span>
                            {isOpen ? (
                              <ChevronUp className="w-5 h-5 flex-shrink-0" />
                            ) : (
                              <ChevronDown className="w-5 h-5 flex-shrink-0" />
                            )}
                          </button>
                          
                          {isOpen && (
                            <div className="p-4 bg-white border-t-2 border-black">
                              <p className="font-bold text-gray-700 leading-relaxed">
                                {faq.answer}
                              </p>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Still Have Questions Section */}
      <section className="py-20 bg-black">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-yellow-400 text-black p-8 md:p-12 brutal-border brutal-shadow">
            <div className="bg-black text-white w-20 h-20 brutal-border brutal-shadow mx-auto mb-6 flex items-center justify-center">
              <MessageCircle className="w-10 h-10" />
            </div>
            <h2 className="brutal-text text-3xl md:text-4xl mb-6">
              STILL HAVE QUESTIONS?
            </h2>
            <p className="text-xl font-bold mb-8">
              Can't find what you're looking for? Book a free strategy call and we'll answer all your questions personally.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                to={createPageUrl("Contact")}
                className="bg-pink-500 text-white px-8 py-4 brutal-border brutal-shadow brutal-text text-lg hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150 inline-flex items-center justify-center"
              >
                BOOK FREE STRATEGY CALL
                <ArrowUpRight className="w-6 h-6 ml-2" />
              </Link>
              
              <a
                href="mailto:<EMAIL>"
                className="bg-white text-black px-8 py-4 brutal-border brutal-shadow brutal-text text-lg hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150 inline-flex items-center justify-center"
              >
                EMAIL US DIRECTLY
              </a>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
