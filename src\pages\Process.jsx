import React from 'react';
import { Link } from 'react-router-dom';
import { createPageUrl } from '@/utils';
import { Search, PenTool, BarChart, Flag, ArrowUpRight, Shield, Clock, Users, CheckCircle } from 'lucide-react';

export default function Process() {
  const processSteps = [
    {
      step: "01",
      title: "CONSULTATION & ANALYSIS",
      description: "We begin with a comprehensive consultation to understand your business needs, current technology infrastructure, and goals. Our team conducts a thorough analysis of your requirements and challenges.",
      icon: Search,
      color: "bg-blue-500",
      bgColor: "bg-blue-100"
    },
    {
      step: "02",
      title: "PLANNING & DESIGN",
      description: "Based on our analysis, we create a detailed project plan and design specifications. We develop wireframes, system architecture, and project timelines tailored to your specific needs.",
      icon: PenTool,
      color: "bg-pink-500",
      bgColor: "bg-pink-100"
    },
    {
      step: "03",
      title: "DEVELOPMENT & IMPLEMENTATION",
      description: "Our skilled team begins the development process using the latest technologies and best practices. We maintain regular communication and provide progress updates throughout the implementation phase.",
      icon: Bar<PERSON>hart,
      color: "bg-green-400",
      bgColor: "bg-green-100"
    },
    {
      step: "04",
      title: "TESTING & DEPLOYMENT",
      description: "We conduct rigorous testing to ensure quality and functionality. After successful testing, we deploy your solution and provide comprehensive training and documentation for your team.",
      icon: Flag,
      color: "bg-yellow-400",
      bgColor: "bg-yellow-100"
    },
    {
      step: "05",
      title: "SUPPORT & MAINTENANCE",
      description: "Our commitment doesn't end at deployment. We provide ongoing support, maintenance, and updates to ensure your technology solutions continue to serve your business effectively.",
      icon: Search,
      color: "bg-purple-500",
      bgColor: "bg-purple-100"
    }
  ];

  return (
    <div className="bg-white">
      {/* Hero Section */}
      <section className="py-20 bg-purple-500">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-white text-black p-8 brutal-border brutal-shadow inline-block transform -rotate-1 mb-8">
            <h1 className="brutal-text text-4xl md:text-6xl">HOW IT WORKS</h1>
          </div>
          <p className="text-xl md:text-2xl font-bold text-white max-w-3xl mx-auto">
            Our comprehensive 5-step process designed to deliver exceptional technology solutions that drive your business forward.
          </p>
        </div>
      </section>

      {/* Process Steps */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="relative">
            {/* Dashed line for desktop */}
            <div className="hidden lg:block absolute top-1/2 left-0 w-full h-1 bg-black border-t-4 border-b-4 border-black border-dotted transform -translate-y-1/2"></div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-8 relative">
              {processSteps.map((item, index) => (
                <div key={index} className={index % 2 === 0 ? "asymmetric-grid" : "anti-asymmetric"}>
                  <div className={`bg-white text-black p-6 brutal-border brutal-shadow hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-300 h-full`}>
                    <div className="flex items-center justify-between mb-4">
                      <div className={`${item.color} text-white w-16 h-16 brutal-border brutal-shadow-small flex items-center justify-center`}>
                        <span className="brutal-text text-2xl">{item.step}</span>
                      </div>
                      <div className={`${item.color} text-white p-2 brutal-border`}>
                        <item.icon className="w-6 h-6" />
                      </div>
                    </div>
                    <h3 className="brutal-text text-xl mb-3">{item.title}</h3>
                    <p className="font-bold text-gray-800">{item.description}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Visual Flow Section */}
       <section className="py-20 bg-gray-100">
        <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <div className="bg-black text-white p-6 brutal-border brutal-shadow inline-block transform rotate-1 mb-12">
              <h2 className="brutal-text text-3xl md:text-4xl">OUR WORKFLOW</h2>
            </div>
            <div className="flex flex-col lg:flex-row items-center justify-center gap-4 text-black brutal-text text-sm lg:text-base">
                <div className="bg-blue-500 p-3 brutal-border brutal-shadow">CONSULTATION</div>
                <div className="text-2xl font-black">&rarr;</div>
                <div className="bg-pink-500 p-3 brutal-border brutal-shadow">PLANNING</div>
                <div className="text-2xl font-black">&rarr;</div>
                <div className="bg-green-400 p-3 brutal-border brutal-shadow">DEVELOPMENT</div>
                <div className="text-2xl font-black">&rarr;</div>
                <div className="bg-yellow-400 p-3 brutal-border brutal-shadow">TESTING</div>
                <div className="text-2xl font-black">&rarr;</div>
                <div className="bg-purple-500 p-3 brutal-border brutal-shadow">SUPPORT</div>
            </div>
       </div>
      </section>

      {/* Our Methodology */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="bg-green-400 text-black p-6 brutal-border brutal-shadow inline-block transform -rotate-1">
              <h2 className="brutal-text text-3xl md:text-4xl">OUR METHODOLOGY</h2>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div className="asymmetric-grid">
              <div className="bg-white brutal-border brutal-shadow p-6 text-center h-full">
                <div className="bg-blue-500 w-16 h-16 brutal-border brutal-shadow-small mx-auto mb-4 flex items-center justify-center">
                  <Shield className="w-8 h-8 text-white" />
                </div>
                <h3 className="brutal-text text-xl mb-3">QUALITY ASSURANCE</h3>
                <p className="font-bold text-gray-600">
                  We follow strict quality standards and testing protocols to ensure every solution meets the highest standards of performance and reliability.
                </p>
              </div>
            </div>

            <div className="anti-asymmetric">
              <div className="bg-white brutal-border brutal-shadow p-6 text-center h-full">
                <div className="bg-pink-500 w-16 h-16 brutal-border brutal-shadow-small mx-auto mb-4 flex items-center justify-center">
                  <Clock className="w-8 h-8 text-white" />
                </div>
                <h3 className="brutal-text text-xl mb-3">TIMELY DELIVERY</h3>
                <p className="font-bold text-gray-600">
                  We understand the importance of deadlines. Our project management ensures on-time delivery without compromising quality.
                </p>
              </div>
            </div>

            <div className="asymmetric-grid">
              <div className="bg-white brutal-border brutal-shadow p-6 text-center h-full">
                <div className="bg-yellow-400 w-16 h-16 brutal-border brutal-shadow-small mx-auto mb-4 flex items-center justify-center">
                  <Users className="w-8 h-8 text-black" />
                </div>
                <h3 className="brutal-text text-xl mb-3">COLLABORATIVE APPROACH</h3>
                <p className="font-bold text-gray-600">
                  We work closely with your team throughout the process, ensuring clear communication and alignment with your business objectives.
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* What You Get */}
      <section className="py-20 bg-gray-100">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="bg-black text-white p-6 brutal-border brutal-shadow inline-block transform rotate-1">
              <h2 className="brutal-text text-3xl md:text-4xl">WHAT YOU GET</h2>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {[
              "Comprehensive project documentation",
              "Source code and technical specifications",
              "User training and support materials",
              "Ongoing maintenance and support",
              "Regular progress updates and reports",
              "Post-deployment optimization",
              "24/7 technical support",
              "Scalable and future-proof solutions"
            ].map((item, index) => (
              <div key={index} className={index % 2 === 0 ? "asymmetric-grid" : "anti-asymmetric"}>
                <div className="bg-white brutal-border brutal-shadow p-4 flex items-center gap-3">
                  <div className="bg-green-400 brutal-border p-1">
                    <CheckCircle className="w-4 h-4 text-black" />
                  </div>
                  <span className="font-bold">{item}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-white">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-yellow-400 text-black p-8 md:p-12 brutal-border brutal-shadow">
            <h2 className="brutal-text text-3xl md:text-4xl mb-6">
              READY TO START YOUR PROJECT?
            </h2>
            <p className="text-xl font-bold mb-8">
              Your journey to digital transformation starts with a free consultation. Let's discuss your technology needs and build a custom solution.
            </p>
            <Link
              to={createPageUrl("Contact")}
              className="bg-pink-500 text-white px-8 py-4 brutal-border brutal-shadow brutal-text text-lg hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150 inline-flex items-center"
            >
              GET YOUR FREE CONSULTATION
              <ArrowUpRight className="w-6 h-6 ml-2" />
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
}
