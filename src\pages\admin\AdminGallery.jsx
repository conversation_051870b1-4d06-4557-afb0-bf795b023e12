import React, { useState } from 'react';
import { Plus, Edit, Trash2, Eye, Upload, Filter } from 'lucide-react';
import DataTable from '../../components/admin/DataTable';
import Modal from '../../components/admin/Modal';
import FormField from '../../components/admin/FormField';
import ActionButton from '../../components/admin/ActionButton';
import FileUpload from '../../components/admin/FileUpload';
import { galleryCategories } from '../../data/galleryImages';
import { useCollection } from '../../hooks/useFirestore';
import { COLLECTIONS, STORAGE_PATHS } from '../../services/firebase';
import { useAuth } from '../../contexts/AuthContext';

const AdminGallery = () => {
  const { user } = useAuth();
  const {
    documents: images,
    loading,
    error,
    addDocument,
    updateDocument,
    deleteDocument
  } = useCollection(COLLECTIONS.GALLERY_IMAGES, {
    orderBy: { field: 'createdAt', direction: 'desc' }
  });

  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingImage, setEditingImage] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [uploadedImageUrl, setUploadedImageUrl] = useState('');
  const [formData, setFormData] = useState({
    title: '',
    alt: '',
    category: '',
    size: 'medium',
    description: '',
    imageUrl: ''
  });

  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const imageData = {
        title: formData.title,
        alt: formData.alt,
        category: formData.category,
        size: formData.size,
        description: formData.description,
        imageUrl: uploadedImageUrl || formData.imageUrl || 'https://images.unsplash.com/photo-1581091226825-a6a2a5aee158?w=600&h=400&fit=crop',
        storageRef: uploadedImageUrl ? 'uploaded_via_storage' : null
      };

      if (editingImage) {
        // Update existing image
        const result = await updateDocument(editingImage.id, imageData);
        if (!result.success) {
          throw new Error(result.error);
        }
      } else {
        // Add new image
        const result = await addDocument(imageData, user?.uid);
        if (!result.success) {
          throw new Error(result.error);
        }
      }

      handleCloseModal();
    } catch (error) {
      console.error('Error saving image:', error);
      alert('Failed to save image. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  // Handle file upload completion
  const handleUploadComplete = (result) => {
    setUploadedImageUrl(result.url);
    setFormData(prev => ({ ...prev, imageUrl: result.url }));
  };

  const handleUploadError = (error) => {
    console.error('Upload error:', error);
    alert(`Upload failed: ${error}`);
  };

  const handleEdit = (image) => {
    setEditingImage(image);
    setFormData({
      title: image.title,
      alt: image.alt,
      category: image.category,
      size: image.size,
      description: image.description,
      imageUrl: image.imageUrl || image.src // Support both old and new field names
    });
    setIsModalOpen(true);
  };

  const handleDelete = async (image) => {
    if (window.confirm(`Are you sure you want to delete "${image.title}"?`)) {
      try {
        const result = await deleteDocument(image.id);
        if (!result.success) {
          throw new Error(result.error);
        }
      } catch (error) {
        console.error('Error deleting image:', error);
        alert('Failed to delete image. Please try again.');
      }
    }
  };

  const handleView = (image) => {
    window.open(image.imageUrl || image.src, '_blank');
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setEditingImage(null);
    setIsSubmitting(false);
    setUploadedImageUrl('');
    setFormData({
      title: '',
      alt: '',
      category: '',
      size: 'medium',
      description: '',
      imageUrl: ''
    });
  };

  const handleAddNew = () => {
    setEditingImage(null);
    setFormData({
      title: '',
      alt: '',
      category: '',
      size: 'medium',
      description: '',
      imageUrl: ''
    });
    setIsModalOpen(true);
  };

  // Filter images by category
  const filteredImages = selectedCategory === 'all' 
    ? images 
    : images.filter(img => img.category === selectedCategory);

  const columns = [
    {
      key: 'imageUrl',
      label: 'IMAGE',
      render: (imageUrl, image) => (
        <img
          src={imageUrl || image.src}
          alt="Gallery item"
          className="w-16 h-16 object-cover brutal-border"
        />
      )
    },
    {
      key: 'title',
      label: 'TITLE',
      sortable: true
    },
    {
      key: 'category',
      label: 'CATEGORY',
      sortable: true,
      render: (category) => {
        const categoryData = galleryCategories.find(cat => cat.id === category);
        return (
          <span className={`px-2 py-1 text-xs font-bold brutal-border ${categoryData?.color || 'bg-gray-500'} text-white`}>
            {categoryData?.name || category.toUpperCase()}
          </span>
        );
      }
    },
    {
      key: 'size',
      label: 'SIZE',
      sortable: true,
      render: (size) => (
        <span className="font-bold uppercase">{size}</span>
      )
    },
    {
      key: 'description',
      label: 'DESCRIPTION',
      render: (description) => (
        <span className="text-sm">{description.length > 50 ? `${description.substring(0, 50)}...` : description}</span>
      )
    }
  ];

  const sizeOptions = [
    { value: 'small', label: 'Small' },
    { value: 'medium', label: 'Medium' },
    { value: 'large', label: 'Large' },
    { value: 'wide', label: 'Wide' },
    { value: 'tall', label: 'Tall' },
    { value: 'square', label: 'Square' }
  ];

  const categoryOptions = galleryCategories
    .filter(cat => cat.id !== 'all')
    .map(cat => ({ value: cat.id, label: cat.name }));

  // Show loading state
  if (loading) {
    return (
      <div className="space-y-6">
        <div className="bg-white brutal-border brutal-shadow p-6">
          <div className="flex items-center justify-center py-12">
            <div className="text-center">
              <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
              <p className="font-bold text-gray-600">Loading gallery images...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Show error state
  if (error) {
    return (
      <div className="space-y-6">
        <div className="bg-red-50 border-2 border-red-500 p-6 brutal-shadow">
          <h3 className="brutal-text text-xl text-red-700 mb-2">Error Loading Gallery</h3>
          <p className="font-bold text-red-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-red-500 text-white px-4 py-2 brutal-border brutal-shadow-small font-bold hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150"
          >
            RETRY
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white brutal-border brutal-shadow p-6">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <div>
            <h1 className="brutal-text text-3xl mb-2">GALLERY MANAGEMENT</h1>
            <p className="font-bold text-gray-600">
              Manage your gallery images, categories, and metadata
            </p>
          </div>
          
          <ActionButton 
            onClick={handleAddNew}
            icon={Plus}
            variant="primary"
          >
            ADD NEW IMAGE
          </ActionButton>
        </div>
      </div>

      {/* Category Filter */}
      <div className="bg-white brutal-border brutal-shadow p-4">
        <div className="flex flex-wrap gap-2">
          {galleryCategories.map((category) => (
            <button
              key={category.id}
              onClick={() => setSelectedCategory(category.id)}
              className={`px-4 py-2 brutal-border brutal-shadow-small font-bold transition-all duration-150 hover:translate-x-1 hover:translate-y-1 hover:shadow-none ${
                selectedCategory === category.id
                  ? `${category.color} text-white`
                  : 'bg-white text-black hover:bg-gray-50'
              }`}
            >
              {category.name}
            </button>
          ))}
        </div>
      </div>

      {/* Data Table */}
      <DataTable
        data={filteredImages}
        columns={columns}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onView={handleView}
        searchable={true}
        filterable={false}
      />

      {/* Add/Edit Modal */}
      <Modal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        title={editingImage ? 'EDIT GALLERY IMAGE' : 'ADD NEW GALLERY IMAGE'}
        size="large"
      >
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              label="Image Title"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              placeholder="Enter image title"
              required
            />
            
            <FormField
              label="Alt Text"
              name="alt"
              value={formData.alt}
              onChange={handleInputChange}
              placeholder="Enter alt text for accessibility"
              required
            />
            
            <FormField
              label="Category"
              type="select"
              name="category"
              value={formData.category}
              onChange={handleInputChange}
              options={categoryOptions}
              required
            />
            
            <FormField
              label="Size"
              type="select"
              name="size"
              value={formData.size}
              onChange={handleInputChange}
              options={sizeOptions}
              required
            />
          </div>
          
          {/* File Upload Section */}
          <div className="space-y-4">
            <label className="block font-bold text-gray-700">Upload Image</label>
            <FileUpload
              storagePath={STORAGE_PATHS.GALLERY}
              acceptedTypes={['image/*']}
              maxSize={10 * 1024 * 1024} // 10MB
              onUploadComplete={handleUploadComplete}
              onUploadError={handleUploadError}
            />
          </div>

          <div className="text-center font-bold text-gray-500">OR</div>

          <FormField
            label="Image URL"
            name="imageUrl"
            value={formData.imageUrl}
            onChange={handleInputChange}
            placeholder="Enter image URL manually"
            required={!editingImage && !uploadedImageUrl}
          />

          <FormField
            label="Description"
            type="textarea"
            name="description"
            value={formData.description}
            onChange={handleInputChange}
            placeholder="Enter image description"
            rows={3}
            required
          />
          
          <div className="flex gap-4 pt-4">
            <ActionButton
              type="submit"
              variant="primary"
              icon={editingImage ? Edit : Plus}
              disabled={isSubmitting}
            >
              {isSubmitting
                ? (editingImage ? 'UPDATING...' : 'ADDING...')
                : (editingImage ? 'UPDATE IMAGE' : 'ADD IMAGE')
              }
            </ActionButton>
            
            <ActionButton
              type="button"
              variant="outline"
              onClick={handleCloseModal}
            >
              CANCEL
            </ActionButton>
          </div>
        </form>
      </Modal>
    </div>
  );
};

export default AdminGallery;
