/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        'primary-blue': '#0066FF',
        'primary-pink': '#FF0066',
        'accent-yellow': '#FFFF00',
        'accent-green': '#00FF66',
        'brutal-black': '#000000',
        'brutal-white': '#FFFFFF',
      },
      fontFamily: {
        'brutal': ['Inter', 'system-ui', 'sans-serif'],
      },
      boxShadow: {
        'brutal': '8px 8px 0px #000000',
        'brutal-small': '4px 4px 0px #000000',
      },
      borderWidth: {
        'brutal': '4px',
      },
    },
  },
  plugins: [],
}
