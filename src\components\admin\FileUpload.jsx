// File Upload Component with Firebase Storage Integration
import React, { useState, useRef } from 'react';
import { Upload, X, Image, File, CheckCircle, AlertCircle } from 'lucide-react';
import { useFileUpload } from '../../hooks/useFirestore';
import { StorageService } from '../../services/firebase';

const FileUpload = ({ 
  onUploadComplete, 
  onUploadError,
  storagePath,
  acceptedTypes = ['image/*'],
  maxSize = 10 * 1024 * 1024, // 10MB default
  multiple = false,
  className = '',
  children
}) => {
  const [dragActive, setDragActive] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState([]);
  const [previews, setPreviews] = useState([]);
  const fileInputRef = useRef(null);
  
  const { uploading, progress, error, uploadFile, uploadMultipleFiles } = useFileUpload();

  // Handle file selection
  const handleFileSelect = (files) => {
    const fileArray = Array.from(files);
    const validFiles = [];
    const invalidFiles = [];

    fileArray.forEach(file => {
      const validation = StorageService.validateFile(file, {
        maxSize,
        allowedTypes: acceptedTypes.includes('image/*') 
          ? ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
          : acceptedTypes,
        allowedExtensions: acceptedTypes.includes('image/*')
          ? ['jpg', 'jpeg', 'png', 'gif', 'webp']
          : ['pdf', 'doc', 'docx', 'txt']
      });

      if (validation.isValid) {
        validFiles.push(file);
      } else {
        invalidFiles.push({ file, errors: validation.errors });
      }
    });

    if (invalidFiles.length > 0) {
      const errorMessage = invalidFiles.map(({ file, errors }) => 
        `${file.name}: ${errors.join(', ')}`
      ).join('\n');
      
      if (onUploadError) {
        onUploadError(errorMessage);
      } else {
        alert(`Invalid files:\n${errorMessage}`);
      }
    }

    if (validFiles.length > 0) {
      setSelectedFiles(multiple ? [...selectedFiles, ...validFiles] : validFiles);
      generatePreviews(multiple ? [...selectedFiles, ...validFiles] : validFiles);
    }
  };

  // Generate file previews
  const generatePreviews = (files) => {
    const newPreviews = files.map(file => {
      if (file.type.startsWith('image/')) {
        return {
          file,
          type: 'image',
          url: URL.createObjectURL(file),
          name: file.name,
          size: StorageService.formatFileSize(file.size)
        };
      } else {
        return {
          file,
          type: 'document',
          url: null,
          name: file.name,
          size: StorageService.formatFileSize(file.size)
        };
      }
    });
    
    setPreviews(newPreviews);
  };

  // Handle file upload
  const handleUpload = async () => {
    if (selectedFiles.length === 0) return;

    try {
      let results;
      
      if (multiple && selectedFiles.length > 1) {
        results = await uploadMultipleFiles(selectedFiles, storagePath);
      } else {
        const result = await uploadFile(selectedFiles[0], storagePath);
        results = { success: result.success, results: [result] };
      }

      if (results.success) {
        if (onUploadComplete) {
          onUploadComplete(multiple ? results.results : results.results[0]);
        }
        
        // Clear selections
        setSelectedFiles([]);
        setPreviews([]);
        if (fileInputRef.current) {
          fileInputRef.current.value = '';
        }
      } else {
        throw new Error(results.error || 'Upload failed');
      }
    } catch (err) {
      if (onUploadError) {
        onUploadError(err.message);
      } else {
        alert(`Upload failed: ${err.message}`);
      }
    }
  };

  // Remove selected file
  const removeFile = (index) => {
    const newFiles = selectedFiles.filter((_, i) => i !== index);
    const newPreviews = previews.filter((_, i) => i !== index);
    
    setSelectedFiles(newFiles);
    setPreviews(newPreviews);
    
    if (fileInputRef.current && newFiles.length === 0) {
      fileInputRef.current.value = '';
    }
  };

  // Drag and drop handlers
  const handleDrag = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (e.type === 'dragenter' || e.type === 'dragover') {
      setDragActive(true);
    } else if (e.type === 'dragleave') {
      setDragActive(false);
    }
  };

  const handleDrop = (e) => {
    e.preventDefault();
    e.stopPropagation();
    setDragActive(false);
    
    if (e.dataTransfer.files && e.dataTransfer.files[0]) {
      handleFileSelect(e.dataTransfer.files);
    }
  };

  const handleInputChange = (e) => {
    if (e.target.files && e.target.files[0]) {
      handleFileSelect(e.target.files);
    }
  };

  const openFileDialog = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* File Input */}
      <input
        ref={fileInputRef}
        type="file"
        multiple={multiple}
        accept={acceptedTypes.join(',')}
        onChange={handleInputChange}
        className="hidden"
      />

      {/* Drop Zone */}
      <div
        className={`border-2 border-dashed p-6 text-center cursor-pointer transition-all duration-150 ${
          dragActive
            ? 'border-blue-500 bg-blue-50'
            : 'border-gray-300 hover:border-gray-400'
        } ${uploading ? 'pointer-events-none opacity-50' : ''}`}
        onDragEnter={handleDrag}
        onDragLeave={handleDrag}
        onDragOver={handleDrag}
        onDrop={handleDrop}
        onClick={openFileDialog}
      >
        {children || (
          <div className="space-y-3">
            <Upload className="w-12 h-12 text-gray-400 mx-auto" />
            <div>
              <p className="font-bold text-gray-600">
                Click to upload or drag and drop
              </p>
              <p className="text-sm text-gray-500">
                {acceptedTypes.includes('image/*') ? 'Images' : 'Files'} up to {StorageService.formatFileSize(maxSize)}
              </p>
            </div>
          </div>
        )}
      </div>

      {/* File Previews */}
      {previews.length > 0 && (
        <div className="space-y-3">
          <h4 className="font-bold text-gray-700">Selected Files:</h4>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
            {previews.map((preview, index) => (
              <div key={index} className="bg-gray-50 p-3 brutal-border relative">
                <button
                  onClick={() => removeFile(index)}
                  className="absolute top-1 right-1 bg-red-500 text-white w-6 h-6 brutal-border brutal-shadow-small flex items-center justify-center hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150"
                >
                  <X className="w-3 h-3" />
                </button>
                
                <div className="flex items-center space-x-3">
                  {preview.type === 'image' ? (
                    <img
                      src={preview.url}
                      alt={preview.name}
                      className="w-12 h-12 object-cover brutal-border"
                    />
                  ) : (
                    <div className="w-12 h-12 bg-gray-200 brutal-border flex items-center justify-center">
                      <File className="w-6 h-6 text-gray-500" />
                    </div>
                  )}
                  
                  <div className="flex-1 min-w-0">
                    <p className="font-bold text-sm truncate">{preview.name}</p>
                    <p className="text-xs text-gray-500">{preview.size}</p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      {/* Upload Progress */}
      {uploading && (
        <div className="bg-blue-50 p-4 brutal-border">
          <div className="flex items-center space-x-3">
            <div className="w-6 h-6 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
            <div className="flex-1">
              <p className="font-bold text-blue-700">Uploading...</p>
              <div className="w-full bg-blue-200 h-2 brutal-border mt-2">
                <div 
                  className="bg-blue-500 h-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                ></div>
              </div>
              <p className="text-sm text-blue-600 mt-1">{Math.round(progress)}% complete</p>
            </div>
          </div>
        </div>
      )}

      {/* Error Display */}
      {error && (
        <div className="bg-red-50 border-2 border-red-500 p-4 brutal-shadow">
          <div className="flex items-center space-x-2">
            <AlertCircle className="w-5 h-5 text-red-500" />
            <p className="font-bold text-red-700">Upload Error</p>
          </div>
          <p className="text-red-600 mt-1">{error}</p>
        </div>
      )}

      {/* Upload Button */}
      {selectedFiles.length > 0 && !uploading && (
        <div className="flex justify-end">
          <button
            onClick={handleUpload}
            className="bg-green-500 text-white px-6 py-2 brutal-border brutal-shadow font-bold hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150 flex items-center space-x-2"
          >
            <Upload className="w-4 h-4" />
            <span>UPLOAD {selectedFiles.length} FILE{selectedFiles.length > 1 ? 'S' : ''}</span>
          </button>
        </div>
      )}
    </div>
  );
};

export default FileUpload;
