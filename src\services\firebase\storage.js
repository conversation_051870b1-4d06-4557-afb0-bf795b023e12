// Firebase Storage Service
import {
  ref,
  uploadBytes,
  uploadBytesResumable,
  getDownloadURL,
  deleteObject,
  listAll,
  getMetadata
} from 'firebase/storage';
import { storage, STORAGE_PATHS } from '../../config/firebase';

/**
 * Firebase Storage service for file operations
 */
export class StorageService {
  /**
   * Upload a file to Firebase Storage
   * @param {File} file - The file to upload
   * @param {string} path - Storage path (use STORAGE_PATHS constants)
   * @param {string} fileName - Optional custom filename
   * @param {function} onProgress - Progress callback function
   * @returns {Promise<{success: boolean, url?: string, ref?: string, error?: string}>}
   */
  static async uploadFile(file, path, fileName = null, onProgress = null) {
    try {
      // Validate file
      if (!file) {
        throw new Error('No file provided');
      }

      // Generate filename if not provided
      const finalFileName = fileName || this.generateFileName(file.name);
      const fullPath = `${path}${finalFileName}`;
      
      // Create storage reference
      const storageRef = ref(storage, fullPath);

      // Upload file
      let uploadTask;
      if (onProgress) {
        // Use resumable upload with progress tracking
        uploadTask = uploadBytesResumable(storageRef, file);
        
        return new Promise((resolve, reject) => {
          uploadTask.on(
            'state_changed',
            (snapshot) => {
              const progress = (snapshot.bytesTransferred / snapshot.totalBytes) * 100;
              onProgress(progress);
            },
            (error) => {
              console.error('Upload error:', error);
              reject({
                success: false,
                error: this.getStorageErrorMessage(error.code)
              });
            },
            async () => {
              try {
                const downloadURL = await getDownloadURL(uploadTask.snapshot.ref);
                resolve({
                  success: true,
                  url: downloadURL,
                  ref: fullPath
                });
              } catch (error) {
                reject({
                  success: false,
                  error: 'Failed to get download URL'
                });
              }
            }
          );
        });
      } else {
        // Simple upload without progress tracking
        const snapshot = await uploadBytes(storageRef, file);
        const downloadURL = await getDownloadURL(snapshot.ref);
        
        return {
          success: true,
          url: downloadURL,
          ref: fullPath
        };
      }
    } catch (error) {
      console.error('Upload file error:', error);
      return {
        success: false,
        error: error.message || 'Failed to upload file'
      };
    }
  }

  /**
   * Upload multiple files
   * @param {FileList|Array} files - Files to upload
   * @param {string} path - Storage path
   * @param {function} onProgress - Progress callback for each file
   * @returns {Promise<{success: boolean, results?: array, error?: string}>}
   */
  static async uploadMultipleFiles(files, path, onProgress = null) {
    try {
      const uploadPromises = Array.from(files).map((file, index) => {
        const progressCallback = onProgress 
          ? (progress) => onProgress(index, progress, file.name)
          : null;
        
        return this.uploadFile(file, path, null, progressCallback);
      });

      const results = await Promise.all(uploadPromises);
      
      // Check if all uploads were successful
      const failedUploads = results.filter(result => !result.success);
      
      if (failedUploads.length > 0) {
        return {
          success: false,
          error: `${failedUploads.length} file(s) failed to upload`,
          results
        };
      }

      return {
        success: true,
        results
      };
    } catch (error) {
      console.error('Upload multiple files error:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * Delete a file from Firebase Storage
   * @param {string} filePath - Full path to the file in storage
   * @returns {Promise<{success: boolean, error?: string}>}
   */
  static async deleteFile(filePath) {
    try {
      const fileRef = ref(storage, filePath);
      await deleteObject(fileRef);
      
      return { success: true };
    } catch (error) {
      console.error('Delete file error:', error);
      return {
        success: false,
        error: this.getStorageErrorMessage(error.code)
      };
    }
  }

  /**
   * Get download URL for a file
   * @param {string} filePath - Full path to the file in storage
   * @returns {Promise<{success: boolean, url?: string, error?: string}>}
   */
  static async getFileURL(filePath) {
    try {
      const fileRef = ref(storage, filePath);
      const url = await getDownloadURL(fileRef);
      
      return {
        success: true,
        url
      };
    } catch (error) {
      console.error('Get file URL error:', error);
      return {
        success: false,
        error: this.getStorageErrorMessage(error.code)
      };
    }
  }

  /**
   * Get file metadata
   * @param {string} filePath - Full path to the file in storage
   * @returns {Promise<{success: boolean, metadata?: object, error?: string}>}
   */
  static async getFileMetadata(filePath) {
    try {
      const fileRef = ref(storage, filePath);
      const metadata = await getMetadata(fileRef);
      
      return {
        success: true,
        metadata
      };
    } catch (error) {
      console.error('Get file metadata error:', error);
      return {
        success: false,
        error: this.getStorageErrorMessage(error.code)
      };
    }
  }

  /**
   * List all files in a directory
   * @param {string} path - Directory path
   * @returns {Promise<{success: boolean, files?: array, error?: string}>}
   */
  static async listFiles(path) {
    try {
      const listRef = ref(storage, path);
      const result = await listAll(listRef);
      
      const files = await Promise.all(
        result.items.map(async (itemRef) => {
          const url = await getDownloadURL(itemRef);
          const metadata = await getMetadata(itemRef);
          
          return {
            name: itemRef.name,
            fullPath: itemRef.fullPath,
            url,
            size: metadata.size,
            contentType: metadata.contentType,
            timeCreated: metadata.timeCreated,
            updated: metadata.updated
          };
        })
      );

      return {
        success: true,
        files
      };
    } catch (error) {
      console.error('List files error:', error);
      return {
        success: false,
        error: this.getStorageErrorMessage(error.code)
      };
    }
  }

  /**
   * Generate a unique filename
   * @param {string} originalName - Original filename
   * @returns {string} - Unique filename
   */
  static generateFileName(originalName) {
    const timestamp = Date.now();
    const randomString = Math.random().toString(36).substring(2, 15);
    const extension = originalName.split('.').pop();
    const nameWithoutExtension = originalName.replace(`.${extension}`, '');
    
    // Clean the filename
    const cleanName = nameWithoutExtension
      .toLowerCase()
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '');
    
    return `${cleanName}-${timestamp}-${randomString}.${extension}`;
  }

  /**
   * Validate file type and size
   * @param {File} file - File to validate
   * @param {object} options - Validation options
   * @returns {object} - Validation result
   */
  static validateFile(file, options = {}) {
    const {
      maxSize = 10 * 1024 * 1024, // 10MB default
      allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
      allowedExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp']
    } = options;

    const errors = [];

    // Check file size
    if (file.size > maxSize) {
      errors.push(`File size must be less than ${this.formatFileSize(maxSize)}`);
    }

    // Check file type
    if (!allowedTypes.includes(file.type)) {
      errors.push(`File type ${file.type} is not allowed`);
    }

    // Check file extension
    const extension = file.name.split('.').pop().toLowerCase();
    if (!allowedExtensions.includes(extension)) {
      errors.push(`File extension .${extension} is not allowed`);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Format file size for display
   * @param {number} bytes - File size in bytes
   * @returns {string} - Formatted file size
   */
  static formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Convert Firebase Storage error codes to user-friendly messages
   * @param {string} errorCode 
   * @returns {string}
   */
  static getStorageErrorMessage(errorCode) {
    const errorMessages = {
      'storage/object-not-found': 'File not found.',
      'storage/unauthorized': 'You do not have permission to access this file.',
      'storage/canceled': 'Upload was canceled.',
      'storage/unknown': 'An unknown error occurred.',
      'storage/invalid-format': 'Invalid file format.',
      'storage/invalid-event-name': 'Invalid event name.',
      'storage/invalid-url': 'Invalid download URL.',
      'storage/invalid-argument': 'Invalid argument provided.',
      'storage/no-default-bucket': 'No default storage bucket configured.',
      'storage/cannot-slice-blob': 'Cannot process file.',
      'storage/server-file-wrong-size': 'File size mismatch.'
    };

    return errorMessages[errorCode] || 'An unexpected storage error occurred.';
  }
}

export default StorageService;
