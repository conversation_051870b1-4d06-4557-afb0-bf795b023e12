import React, { useState } from "react";
import { Link } from "react-router-dom";
import { caseStudies } from "@/data/caseStudies";
import { createPageUrl } from "@/utils";
import { ArrowUpRight, TrendingUp, Target, Users, DollarSign, BarChart3 } from "lucide-react";

export default function CaseStudies() {
  const [selectedStudy, setSelectedStudy] = useState(null);

  const getIndustryColor = (industry) => {
    const colors = {
      "SaaS": "bg-blue-500",
      "Fitness": "bg-green-400",
      "Beauty & Cosmetics": "bg-pink-500",
      "E-commerce": "bg-yellow-400",
      "Healthcare": "bg-purple-500"
    };
    return colors[industry] || "bg-gray-500";
  };

  const getMetricIcon = (metric) => {
    const icons = {
      "Demo Requests": Target,
      "Cost per Lead": DollarSign,
      "Revenue Attribution": DollarSign,
      "ROAS": TrendingUp,
      "New Memberships": Users,
      "Social Media Revenue": DollarSign,
      "Member Retention": Users,
      "Cost per Acquisition": DollarSign,
      "Online Sales": TrendingUp,
      "Brand Mentions": BarChart3,
      "Email Signups": Users
    };
    return icons[metric] || BarChart3;
  };

  return (
    <div className="min-h-screen bg-white">
      {/* Hero Section */}
      <section className="py-20 bg-blue-500">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="anti-asymmetric">
            <div className="bg-white text-black p-8 brutal-border brutal-shadow inline-block">
              <h1 className="brutal-text text-4xl md:text-6xl mb-4">CASE STUDIES</h1>
              <p className="text-xl font-bold max-w-2xl mx-auto">
                Real businesses. Real results. Real revenue growth through strategic social media marketing.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Case Studies Overview */}
      <section className="py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <div className="bg-yellow-400 text-black p-4 brutal-border brutal-shadow inline-block transform rotate-1">
              <h2 className="brutal-text text-2xl md:text-3xl">SUCCESS STORIES</h2>
            </div>
            <p className="text-lg font-bold text-gray-700 mt-6 max-w-3xl mx-auto">
              Click on any case study to see the detailed breakdown of our strategy, implementation, and results.
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {caseStudies.map((study, index) => (
              <div 
                key={study.id} 
                className={`cursor-pointer ${index % 2 === 0 ? 'asymmetric-grid' : 'anti-asymmetric'}`}
                onClick={() => setSelectedStudy(study)}
              >
                <div className={`bg-white brutal-border brutal-shadow p-6 hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-300 h-full ${selectedStudy?.id === study.id ? 'ring-4 ring-pink-500' : ''}`}>
                  {study.featured_image_url && (
                    <div className="bg-gray-200 brutal-border brutal-shadow-small mb-4 overflow-hidden">
                      <img 
                        src={study.featured_image_url} 
                        alt={study.client_name}
                        className="w-full h-32 object-cover"
                      />
                    </div>
                  )}

                  {/* Industry Badge */}
                  <div className={`${getIndustryColor(study.industry)} text-white px-3 py-1 brutal-text text-xs inline-block mb-4`}>
                    {study.industry.toUpperCase()}
                  </div>

                  {/* Client Name */}
                  <h3 className="brutal-text text-xl mb-3">{study.client_name}</h3>

                  {/* Challenge Preview */}
                  <p className="font-bold text-gray-600 mb-4 line-clamp-3">
                    {study.challenge}
                  </p>

                  {/* Key Results */}
                  <div className="grid grid-cols-2 gap-2 mb-4">
                    {study.results.slice(0, 4).map((result, resultIndex) => (
                      <div key={resultIndex} className="bg-gray-100 p-2 text-center">
                        <div className="brutal-text text-lg text-green-600">{result.value}</div>
                        <div className="text-xs font-bold text-gray-600">{result.metric}</div>
                      </div>
                    ))}
                  </div>

                  {/* Services Used */}
                  <div className="flex flex-wrap gap-1 mb-4">
                    {study.services_used.slice(0, 2).map((service, serviceIndex) => (
                      <span 
                        key={serviceIndex}
                        className="bg-black text-white px-2 py-1 text-xs font-bold"
                      >
                        {service}
                      </span>
                    ))}
                    {study.services_used.length > 2 && (
                      <span className="bg-gray-300 text-black px-2 py-1 text-xs font-bold">
                        +{study.services_used.length - 2} more
                      </span>
                    )}
                  </div>

                  {/* View Details Button */}
                  <button className="w-full bg-pink-500 text-white px-4 py-2 brutal-text text-sm hover:bg-blue-500 transition-colors duration-200 inline-flex items-center justify-center gap-2">
                    VIEW FULL CASE STUDY
                    <ArrowUpRight className="w-4 h-4" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Detailed Case Study Modal/Section */}
      {selectedStudy && (
        <section className="py-20 bg-gray-100">
          <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
            <div className="bg-white brutal-border brutal-shadow p-8 md:p-12">
              
              {/* Header */}
              <div className="flex items-center justify-between mb-8">
                <div>
                  <div className={`${getIndustryColor(selectedStudy.industry)} text-white px-4 py-2 brutal-text text-sm inline-block mb-4`}>
                    {selectedStudy.industry.toUpperCase()}
                  </div>
                  <h2 className="brutal-text text-3xl md:text-4xl">{selectedStudy.client_name}</h2>
                </div>
                <button 
                  onClick={() => setSelectedStudy(null)}
                  className="bg-gray-200 text-black px-4 py-2 brutal-border brutal-shadow-small hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150"
                >
                  ✕
                </button>
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
                
                {/* Left Column - Challenge & Solution */}
                <div>
                  <div className="mb-8">
                    <h3 className="brutal-text text-xl mb-4 bg-red-500 text-white px-4 py-2 inline-block">
                      THE CHALLENGE
                    </h3>
                    <p className="font-bold text-gray-700 leading-relaxed">
                      {selectedStudy.challenge}
                    </p>
                  </div>

                  <div className="mb-8">
                    <h3 className="brutal-text text-xl mb-4 bg-blue-500 text-white px-4 py-2 inline-block">
                      OUR SOLUTION
                    </h3>
                    <p className="font-bold text-gray-700 leading-relaxed">
                      {selectedStudy.solution}
                    </p>
                  </div>

                  {/* Services Used */}
                  <div>
                    <h3 className="brutal-text text-xl mb-4 bg-green-400 text-black px-4 py-2 inline-block">
                      SERVICES USED
                    </h3>
                    <div className="flex flex-wrap gap-2">
                      {selectedStudy.services_used.map((service, index) => (
                        <span 
                          key={index}
                          className="bg-black text-white px-3 py-2 brutal-text text-sm"
                        >
                          {service}
                        </span>
                      ))}
                    </div>
                  </div>
                </div>

                {/* Right Column - Results */}
                <div>
                  <h3 className="brutal-text text-xl mb-6 bg-yellow-400 text-black px-4 py-2 inline-block">
                    THE RESULTS
                  </h3>
                  
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-8">
                    {selectedStudy.results.map((result, index) => {
                      const IconComponent = getMetricIcon(result.metric);
                      return (
                        <div key={index} className="bg-gray-100 brutal-border brutal-shadow-small p-4 text-center">
                          <div className="bg-green-400 w-12 h-12 brutal-border brutal-shadow-small mx-auto mb-3 flex items-center justify-center">
                            <IconComponent className="w-6 h-6 text-black" />
                          </div>
                          <div className="brutal-text text-2xl text-green-600 mb-1">{result.value}</div>
                          <div className="font-bold text-sm text-gray-600">{result.metric}</div>
                          <div className="text-xs font-bold text-gray-500">{result.improvement}</div>
                        </div>
                      );
                    })}
                  </div>

                  {/* Testimonial */}
                  <div className="bg-yellow-400 text-black p-6 brutal-border brutal-shadow">
                    <blockquote className="font-bold text-lg mb-4 leading-relaxed">
                      "{selectedStudy.testimonial}"
                    </blockquote>
                    <cite className="brutal-text text-sm">
                      — {selectedStudy.testimonial_author}
                    </cite>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
      )}

      {/* CTA Section */}
      <section className="py-20 bg-black">
        <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <div className="bg-pink-500 text-white p-8 md:p-12 brutal-border brutal-shadow">
            <h2 className="brutal-text text-3xl md:text-4xl mb-6">
              WANT RESULTS LIKE THESE?
            </h2>
            <p className="text-xl font-bold mb-8">
              Every business is different, but the results speak for themselves. 
              Let's create your success story.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link 
                to={createPageUrl("Contact")}
                className="bg-white text-black px-8 py-4 brutal-border brutal-shadow brutal-text text-lg hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150 inline-flex items-center justify-center"
              >
                GET YOUR FREE STRATEGY CALL
                <ArrowUpRight className="w-6 h-6 ml-2" />
              </Link>
              
              <Link 
                to={createPageUrl("Services")}
                className="bg-yellow-400 text-black px-8 py-4 brutal-border brutal-shadow brutal-text text-lg hover:translate-x-2 hover:translate-y-2 hover:shadow-none transition-all duration-150 inline-flex items-center justify-center"
              >
                VIEW OUR SERVICES
              </Link>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
