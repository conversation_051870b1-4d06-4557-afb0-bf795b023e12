import React from 'react';

const StatsCard = ({ 
  title, 
  value, 
  icon: Icon, 
  color = 'blue', 
  trend, 
  trendValue,
  className = '' 
}) => {
  const colorClasses = {
    blue: 'bg-primary-blue text-white',
    yellow: 'bg-accent-yellow text-black',
    green: 'bg-accent-green text-black',
    red: 'bg-red-500 text-white',
    pink: 'bg-primary-pink text-white',
    purple: 'bg-purple-500 text-white',
    gray: 'bg-gray-500 text-white'
  };

  const trendColors = {
    up: 'text-green-600',
    down: 'text-red-600',
    neutral: 'text-gray-600'
  };

  // Add asymmetric rotation for visual interest
  const asymmetricClass = Math.random() > 0.5 ? 'asymmetric-grid' : 'anti-asymmetric';

  return (
    <div className={`bg-white brutal-border brutal-shadow p-4 sm:p-6 hover:translate-x-1 hover:translate-y-1 hover:shadow-brutal-small transition-all duration-150 ${asymmetricClass} ${className}`}>
      <div className="flex items-center justify-between mb-4">
        <div className={`p-2 sm:p-3 brutal-border brutal-shadow-small ${colorClasses[color]} transform hover:scale-105 transition-transform duration-150`}>
          {Icon && <Icon className="w-5 h-5 sm:w-6 sm:h-6" />}
        </div>

        {trend && trendValue && (
          <div className={`text-xs sm:text-sm font-bold ${trendColors[trend]} bg-white px-2 py-1 brutal-border brutal-shadow-small`}>
            {trend === 'up' ? '↗' : trend === 'down' ? '↘' : '→'} {trendValue}
          </div>
        )}
      </div>

      <div className="space-y-1 sm:space-y-2">
        <h3 className="brutal-text text-xl sm:text-2xl">{value}</h3>
        <p className="font-bold text-gray-600 text-xs sm:text-sm uppercase tracking-wider">{title}</p>
      </div>
    </div>
  );
};

export default StatsCard;
