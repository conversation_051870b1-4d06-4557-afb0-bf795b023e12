// Custom React hooks for Firestore operations
import { useState, useEffect, useCallback } from 'react';
import { FirestoreService } from '../services/firebase';

/**
 * Hook for managing a collection of documents
 * @param {string} collectionName - Firestore collection name
 * @param {object} options - Query options
 * @returns {object} - Collection state and methods
 */
export const useCollection = (collectionName, options = {}) => {
  const [documents, setDocuments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch documents
  const fetchDocuments = useCallback(async () => {
    setLoading(true);
    setError(null);

    try {
      const result = await FirestoreService.getDocuments(collectionName, options);
      
      if (result.success) {
        setDocuments(result.data);
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [collectionName, JSON.stringify(options)]);

  // Add document
  const addDocument = useCallback(async (data, createdBy = null) => {
    try {
      const result = await FirestoreService.addDocument(collectionName, data, createdBy);
      
      if (result.success) {
        // Refresh the collection
        await fetchDocuments();
        return { success: true, id: result.id };
      } else {
        return { success: false, error: result.error };
      }
    } catch (err) {
      return { success: false, error: err.message };
    }
  }, [collectionName, fetchDocuments]);

  // Update document
  const updateDocument = useCallback(async (docId, data) => {
    try {
      const result = await FirestoreService.updateDocument(collectionName, docId, data);
      
      if (result.success) {
        // Update local state
        setDocuments(prev => prev.map(doc => 
          doc.id === docId ? { ...doc, ...data } : doc
        ));
        return { success: true };
      } else {
        return { success: false, error: result.error };
      }
    } catch (err) {
      return { success: false, error: err.message };
    }
  }, [collectionName]);

  // Delete document
  const deleteDocument = useCallback(async (docId) => {
    try {
      const result = await FirestoreService.deleteDocument(collectionName, docId);
      
      if (result.success) {
        // Update local state
        setDocuments(prev => prev.filter(doc => doc.id !== docId));
        return { success: true };
      } else {
        return { success: false, error: result.error };
      }
    } catch (err) {
      return { success: false, error: err.message };
    }
  }, [collectionName]);

  // Initial fetch
  useEffect(() => {
    fetchDocuments();
  }, [fetchDocuments]);

  return {
    documents,
    loading,
    error,
    addDocument,
    updateDocument,
    deleteDocument,
    refetch: fetchDocuments
  };
};

/**
 * Hook for managing a single document
 * @param {string} collectionName - Firestore collection name
 * @param {string} docId - Document ID
 * @returns {object} - Document state and methods
 */
export const useDocument = (collectionName, docId) => {
  const [document, setDocument] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Fetch document
  const fetchDocument = useCallback(async () => {
    if (!docId) {
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const result = await FirestoreService.getDocument(collectionName, docId);
      
      if (result.success) {
        setDocument(result.data);
      } else {
        setError(result.error);
      }
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  }, [collectionName, docId]);

  // Update document
  const updateDocument = useCallback(async (data) => {
    try {
      const result = await FirestoreService.updateDocument(collectionName, docId, data);
      
      if (result.success) {
        // Update local state
        setDocument(prev => ({ ...prev, ...data }));
        return { success: true };
      } else {
        return { success: false, error: result.error };
      }
    } catch (err) {
      return { success: false, error: err.message };
    }
  }, [collectionName, docId]);

  // Initial fetch
  useEffect(() => {
    fetchDocument();
  }, [fetchDocument]);

  return {
    document,
    loading,
    error,
    updateDocument,
    refetch: fetchDocument
  };
};

/**
 * Hook for real-time collection updates
 * @param {string} collectionName - Firestore collection name
 * @param {object} options - Query options
 * @returns {object} - Collection state
 */
export const useRealtimeCollection = (collectionName, options = {}) => {
  const [documents, setDocuments] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    setLoading(true);
    setError(null);

    const unsubscribe = FirestoreService.subscribeToCollection(
      collectionName,
      (data, err) => {
        if (err) {
          setError(err.message);
        } else {
          setDocuments(data || []);
        }
        setLoading(false);
      },
      options
    );

    return () => unsubscribe();
  }, [collectionName, JSON.stringify(options)]);

  return {
    documents,
    loading,
    error
  };
};

/**
 * Hook for real-time document updates
 * @param {string} collectionName - Firestore collection name
 * @param {string} docId - Document ID
 * @returns {object} - Document state
 */
export const useRealtimeDocument = (collectionName, docId) => {
  const [document, setDocument] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    if (!docId) {
      setLoading(false);
      return;
    }

    setLoading(true);
    setError(null);

    const unsubscribe = FirestoreService.subscribeToDocument(
      collectionName,
      docId,
      (data, err) => {
        if (err) {
          setError(err.message);
        } else {
          setDocument(data);
        }
        setLoading(false);
      }
    );

    return () => unsubscribe();
  }, [collectionName, docId]);

  return {
    document,
    loading,
    error
  };
};

/**
 * Hook for file upload operations
 * @returns {object} - Upload state and methods
 */
export const useFileUpload = () => {
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState(null);

  const uploadFile = useCallback(async (file, path, fileName = null) => {
    setUploading(true);
    setProgress(0);
    setError(null);

    try {
      const { StorageService } = await import('../services/firebase');
      
      const result = await StorageService.uploadFile(
        file,
        path,
        fileName,
        (progressValue) => setProgress(progressValue)
      );

      if (result.success) {
        setUploading(false);
        return { success: true, url: result.url, ref: result.ref };
      } else {
        setError(result.error);
        setUploading(false);
        return { success: false, error: result.error };
      }
    } catch (err) {
      setError(err.message);
      setUploading(false);
      return { success: false, error: err.message };
    }
  }, []);

  const uploadMultipleFiles = useCallback(async (files, path) => {
    setUploading(true);
    setProgress(0);
    setError(null);

    try {
      const { StorageService } = await import('../services/firebase');
      
      const result = await StorageService.uploadMultipleFiles(
        files,
        path,
        (index, progressValue, fileName) => {
          // Calculate overall progress
          const overallProgress = ((index + progressValue / 100) / files.length) * 100;
          setProgress(overallProgress);
        }
      );

      setUploading(false);
      
      if (result.success) {
        return { success: true, results: result.results };
      } else {
        setError(result.error);
        return { success: false, error: result.error };
      }
    } catch (err) {
      setError(err.message);
      setUploading(false);
      return { success: false, error: err.message };
    }
  }, []);

  return {
    uploading,
    progress,
    error,
    uploadFile,
    uploadMultipleFiles
  };
};
