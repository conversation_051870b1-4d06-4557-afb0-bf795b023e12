// Firebase Setup Component for Admin
import React, { useState, useEffect } from 'react';
import { Database, Upload, CheckCircle, AlertCircle, Loader } from 'lucide-react';
import { isFirebaseConfigured } from '../../config/firebase';
import { DataMigration } from '../../utils/dataMigration';
import { useAuth } from '../../contexts/AuthContext';

const FirebaseSetup = () => {
  const [isConfigured, setIsConfigured] = useState(false);
  const [migrationStatus, setMigrationStatus] = useState(null);
  const [isMigrating, setIsMigrating] = useState(false);
  const [migrationResults, setMigrationResults] = useState(null);
  const [showAdvanced, setShowAdvanced] = useState(false);
  const { user } = useAuth();

  useEffect(() => {
    // Check if Firebase is configured
    setIsConfigured(isFirebaseConfigured());
    
    // Check migration status
    checkMigrationStatus();
  }, []);

  const checkMigrationStatus = async () => {
    try {
      const status = await DataMigration.checkMigrationStatus();
      setMigrationStatus(status);
    } catch (error) {
      console.error('Error checking migration status:', error);
    }
  };

  const handleMigration = async () => {
    if (!user) {
      alert('You must be logged in to perform data migration.');
      return;
    }

    setIsMigrating(true);
    setMigrationResults(null);

    try {
      const results = await DataMigration.migrateAllData(user.uid);
      setMigrationResults(results);
      
      if (results.success) {
        // Refresh migration status
        await checkMigrationStatus();
      }
    } catch (error) {
      console.error('Migration error:', error);
      setMigrationResults({
        success: false,
        error: error.message
      });
    } finally {
      setIsMigrating(false);
    }
  };

  const handleClearData = async () => {
    if (!user) {
      alert('You must be logged in to clear data.');
      return;
    }

    if (!window.confirm('Are you sure you want to clear ALL data? This action cannot be undone!')) {
      return;
    }

    setIsMigrating(true);
    setMigrationResults(null);

    try {
      const result = await DataMigration.clearAllData();
      setMigrationResults(result);

      if (result.success) {
        await checkMigrationStatus();
      }
    } catch (error) {
      console.error('Clear data error:', error);
      setMigrationResults({
        success: false,
        error: error.message
      });
    } finally {
      setIsMigrating(false);
    }
  };

  const handleCreateAdminUser = async () => {
    if (!user) {
      alert('You must be logged in to create admin user.');
      return;
    }

    const email = prompt('Enter admin email:', '<EMAIL>');
    if (!email) return;

    setIsMigrating(true);
    setMigrationResults(null);

    try {
      const result = await DataMigration.createAdminUser(email);
      setMigrationResults(result);

      if (result.success) {
        alert(`Admin user document created for ${email}!\nNote: You still need to create the actual Firebase Auth user in the Firebase Console.`);
      }
    } catch (error) {
      console.error('Create admin user error:', error);
      setMigrationResults({
        success: false,
        error: error.message
      });
    } finally {
      setIsMigrating(false);
    }
  };

  if (!isConfigured) {
    return (
      <div className="bg-red-50 border-2 border-red-500 p-6 brutal-shadow">
        <div className="flex items-center gap-3 mb-4">
          <AlertCircle className="w-6 h-6 text-red-500" />
          <h3 className="brutal-text text-xl text-red-700">Firebase Not Configured</h3>
        </div>
        
        <div className="space-y-4">
          <p className="font-bold text-red-600">
            Firebase is not properly configured. Please follow these steps:
          </p>
          
          <ol className="list-decimal list-inside space-y-2 text-sm text-red-600">
            <li>Create a Firebase project at <a href="https://console.firebase.google.com" target="_blank" rel="noopener noreferrer" className="underline">Firebase Console</a></li>
            <li>Enable Authentication, Firestore Database, and Storage</li>
            <li>Create a web app and copy the configuration</li>
            <li>Create a <code className="bg-red-100 px-1 rounded">.env</code> file based on <code className="bg-red-100 px-1 rounded">.env.example</code></li>
            <li>Add your Firebase configuration to the environment variables</li>
            <li>Restart the development server</li>
          </ol>
          
          <div className="bg-red-100 p-4 rounded border">
            <h4 className="font-bold text-red-700 mb-2">Required Environment Variables:</h4>
            <pre className="text-xs text-red-600 whitespace-pre-wrap">
{`VITE_FIREBASE_API_KEY=your_api_key
VITE_FIREBASE_AUTH_DOMAIN=your_project.firebaseapp.com
VITE_FIREBASE_PROJECT_ID=your_project_id
VITE_FIREBASE_STORAGE_BUCKET=your_project.appspot.com
VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
VITE_FIREBASE_APP_ID=your_app_id`}
            </pre>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Firebase Status */}
      <div className="bg-green-50 border-2 border-green-500 p-6 brutal-shadow">
        <div className="flex items-center gap-3 mb-4">
          <CheckCircle className="w-6 h-6 text-green-500" />
          <h3 className="brutal-text text-xl text-green-700">Firebase Configured</h3>
        </div>
        <p className="font-bold text-green-600">
          Firebase is properly configured and ready to use.
        </p>
      </div>

      {/* Data Migration Section */}
      <div className="bg-white border-2 border-black p-6 brutal-shadow">
        <div className="flex items-center gap-3 mb-4">
          <Database className="w-6 h-6" />
          <h3 className="brutal-text text-xl">Data Migration</h3>
        </div>

        {migrationStatus && (
          <div className="mb-6">
            <h4 className="font-bold mb-3">Current Database Status:</h4>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {Object.entries(migrationStatus.status || {}).map(([collection, status]) => (
                <div key={collection} className="bg-gray-50 p-3 border brutal-border">
                  <div className="flex items-center justify-between">
                    <span className="font-bold text-sm">{collection.toUpperCase()}</span>
                    {status.hasData ? (
                      <CheckCircle className="w-4 h-4 text-green-500" />
                    ) : (
                      <AlertCircle className="w-4 h-4 text-yellow-500" />
                    )}
                  </div>
                  <p className="text-xs text-gray-600 mt-1">
                    {status.hasData ? 'Has data' : 'No data'}
                  </p>
                </div>
              ))}
            </div>
          </div>
        )}

        <div className="space-y-4">
          <p className="font-bold">
            Migrate dummy data from local files to Firebase Firestore:
          </p>
          
          <div className="flex flex-wrap gap-3">
            <button
              onClick={handleMigration}
              disabled={isMigrating}
              className="bg-blue-500 text-white px-6 py-3 brutal-border brutal-shadow font-bold hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150 disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
            >
              {isMigrating ? (
                <>
                  <Loader className="w-5 h-5 animate-spin" />
                  MIGRATING DATA...
                </>
              ) : (
                <>
                  <Upload className="w-5 h-5" />
                  MIGRATE DATA TO FIREBASE
                </>
              )}
            </button>

            <button
              onClick={() => setShowAdvanced(!showAdvanced)}
              className="bg-gray-500 text-white px-4 py-3 brutal-border brutal-shadow font-bold hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150"
            >
              {showAdvanced ? 'HIDE ADVANCED' : 'SHOW ADVANCED'}
            </button>
          </div>

          {showAdvanced && (
            <div className="bg-yellow-50 border-2 border-yellow-500 p-4 brutal-shadow space-y-3">
              <h4 className="font-bold text-yellow-700">Advanced Operations</h4>
              <p className="text-sm text-yellow-600">
                ⚠️ These operations are for development and testing only!
              </p>

              <div className="flex flex-wrap gap-3">
                <button
                  onClick={handleClearData}
                  disabled={isMigrating}
                  className="bg-red-500 text-white px-4 py-2 brutal-border brutal-shadow-small font-bold hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  CLEAR ALL DATA
                </button>

                <button
                  onClick={handleCreateAdminUser}
                  disabled={isMigrating}
                  className="bg-purple-500 text-white px-4 py-2 brutal-border brutal-shadow-small font-bold hover:translate-x-1 hover:translate-y-1 hover:shadow-none transition-all duration-150 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  CREATE ADMIN USER
                </button>
              </div>
            </div>
          )}

          {migrationResults && (
            <div className={`p-4 border-2 brutal-shadow ${
              migrationResults.success 
                ? 'bg-green-50 border-green-500' 
                : 'bg-red-50 border-red-500'
            }`}>
              <h4 className={`font-bold mb-2 ${
                migrationResults.success ? 'text-green-700' : 'text-red-700'
              }`}>
                Migration {migrationResults.success ? 'Completed' : 'Failed'}
              </h4>
              
              {migrationResults.success && migrationResults.results && (
                <div className="space-y-2">
                  {Object.entries(migrationResults.results).map(([collection, result]) => (
                    <div key={collection} className="flex justify-between text-sm">
                      <span className="font-bold">{collection}:</span>
                      <span className={result.success ? 'text-green-600' : 'text-red-600'}>
                        {result.success ? `${result.count}/${result.total} migrated` : 'Failed'}
                      </span>
                    </div>
                  ))}
                </div>
              )}
              
              {!migrationResults.success && (
                <p className="text-red-600 font-bold">
                  {migrationResults.error}
                </p>
              )}
            </div>
          )}
        </div>
      </div>

      {/* Instructions */}
      <div className="bg-blue-50 border-2 border-blue-500 p-6 brutal-shadow">
        <h3 className="brutal-text text-xl text-blue-700 mb-4">Next Steps</h3>
        <ol className="list-decimal list-inside space-y-2 text-blue-600 font-bold">
          <li>Create an admin user in Firebase Authentication</li>
          <li>Run the data migration to populate your database</li>
          <li>Configure Firestore security rules</li>
          <li>Set up Firebase Storage for file uploads</li>
          <li>Test all admin panel functionality</li>
        </ol>
      </div>
    </div>
  );
};

export default FirebaseSetup;
