// Gallery images data for on-generalservices
export const galleryImages = [
  {
    id: 1,
    src: 'https://images.unsplash.com/photo-1551650975-87deedd944c3?w=800&h=600&fit=crop',
    alt: 'Software Development Project - Mobile App Interface',
    title: 'Mobile App Development',
    category: 'software',
    size: 'large',
    description: 'Custom mobile application with modern UI/UX design'
  },
  {
    id: 2,
    src: 'https://images.unsplash.com/photo-1516321318423-f06f85e504b3?w=600&h=800&fit=crop',
    alt: 'Video Production Setup',
    title: 'Professional Video Production',
    category: 'media',
    size: 'tall',
    description: 'High-quality video production for corporate clients'
  },
  {
    id: 3,
    src: 'https://images.unsplash.com/photo-1561070791-2526d30994b5?w=600&h=400&fit=crop',
    alt: 'Graphic Design Portfolio',
    title: 'Brand Identity Design',
    category: 'design',
    size: 'medium',
    description: 'Complete brand identity package with logo and materials'
  },
  {
    id: 4,
    src: 'https://images.unsplash.com/photo-1611224923853-80b023f02d71?w=600&h=600&fit=crop',
    alt: 'Social Media Campaign',
    title: 'Social Media Marketing',
    category: 'marketing',
    size: 'square',
    description: 'Engaging social media campaigns that drive results'
  },
  {
    id: 5,
    src: 'https://images.unsplash.com/photo-1460925895917-afdab827c52f?w=800&h=500&fit=crop',
    alt: 'Web Development Project',
    title: 'E-commerce Website',
    category: 'software',
    size: 'wide',
    description: 'Full-stack e-commerce solution with payment integration'
  },
  {
    id: 6,
    src: 'https://images.unsplash.com/photo-1598300042247-d088f8ab3a91?w=600&h=800&fit=crop',
    alt: 'Music Recording Session',
    title: 'Music Production',
    category: 'media',
    size: 'tall',
    description: 'Professional music recording and production services'
  },
  {
    id: 7,
    src: 'https://images.unsplash.com/photo-1586717791821-3f44a563fa4c?w=600&h=400&fit=crop',
    alt: 'Print Design Materials',
    title: 'Large Format Printing',
    category: 'design',
    size: 'medium',
    description: 'High-quality large format printing for events and marketing'
  },
  {
    id: 8,
    src: 'https://images.unsplash.com/photo-1553877522-43269d4ea984?w=600&h=600&fit=crop',
    alt: 'Digital Marketing Analytics',
    title: 'Analytics Dashboard',
    category: 'marketing',
    size: 'square',
    description: 'Comprehensive analytics and reporting solutions'
  },
  {
    id: 9,
    src: 'https://images.unsplash.com/photo-1581291518857-4e27b48ff24e?w=800&h=600&fit=crop',
    alt: 'Desktop Application',
    title: 'Desktop Software',
    category: 'software',
    size: 'large',
    description: 'Custom desktop applications for business automation'
  },
  {
    id: 10,
    src: 'https://images.unsplash.com/photo-1574717024653-61fd2cf4d44d?w=600&h=800&fit=crop',
    alt: 'Photography Session',
    title: 'Professional Photography',
    category: 'media',
    size: 'tall',
    description: 'Corporate and event photography services'
  },
  {
    id: 11,
    src: 'https://images.unsplash.com/photo-1626785774573-4b799315345d?w=600&h=400&fit=crop',
    alt: 'Logo Design Collection',
    title: 'Logo Design Portfolio',
    category: 'design',
    size: 'medium',
    description: 'Creative logo designs for various industries'
  },
  {
    id: 12,
    src: 'https://images.unsplash.com/photo-1432888622747-4eb9a8efeb07?w=800&h=500&fit=crop',
    alt: 'Content Marketing Strategy',
    title: 'Content Strategy',
    category: 'marketing',
    size: 'wide',
    description: 'Strategic content marketing that engages audiences'
  }
];

// Categories for filtering
export const galleryCategories = [
  { id: 'all', name: 'ALL PROJECTS', color: 'bg-black' },
  { id: 'software', name: 'SOFTWARE', color: 'bg-blue-500' },
  { id: 'media', name: 'MEDIA', color: 'bg-pink-500' },
  { id: 'design', name: 'DESIGN', color: 'bg-yellow-400' },
  { id: 'marketing', name: 'MARKETING', color: 'bg-green-400' }
];

// Get images by category
export const getImagesByCategory = (category) => {
  if (category === 'all') return galleryImages;
  return galleryImages.filter(image => image.category === category);
};
