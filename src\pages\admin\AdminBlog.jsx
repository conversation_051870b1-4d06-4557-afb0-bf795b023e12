import React, { useState } from 'react';
import { Plus, Edit, Trash2, Eye, BookO<PERSON>, Clock, Tag } from 'lucide-react';
import DataTable from '../../components/admin/DataTable';
import Modal from '../../components/admin/Modal';
import FormField from '../../components/admin/FormField';
import ActionButton from '../../components/admin/ActionButton';
import { blogPosts } from '../../data/blogPosts';

const AdminBlog = () => {
  const [posts, setPosts] = useState(blogPosts);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [editingPost, setEditingPost] = useState(null);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [formData, setFormData] = useState({
    title: '',
    slug: '',
    excerpt: '',
    content: '',
    category: '',
    tags: [],
    read_time: 5,
    published: false,
    featured_image_url: ''
  });

  const blogCategories = [
    { value: 'strategy', label: 'Strategy' },
    { value: 'tips', label: 'Tips & Tricks' },
    { value: 'case-study', label: 'Case Study' },
    { value: 'tutorial', label: 'Tutorial' },
    { value: 'news', label: 'News' },
    { value: 'industry', label: 'Industry Insights' }
  ];

  const generateSlug = (title) => {
    return title
      .toLowerCase()
      .replace(/[^a-z0-9 -]/g, '')
      .replace(/\s+/g, '-')
      .replace(/-+/g, '-')
      .trim('-');
  };

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;
    let newValue = type === 'checkbox' ? checked : value;
    
    setFormData(prev => {
      const updated = { ...prev, [name]: newValue };
      
      // Auto-generate slug when title changes
      if (name === 'title' && !editingPost) {
        updated.slug = generateSlug(value);
      }
      
      return updated;
    });
  };

  const handleTagsChange = (e) => {
    const tags = e.target.value.split(',').map(tag => tag.trim()).filter(tag => tag);
    setFormData(prev => ({
      ...prev,
      tags
    }));
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    
    if (editingPost) {
      // Update existing post
      setPosts(prev => prev.map(post => 
        post.id === editingPost.id 
          ? { 
              ...post, 
              ...formData,
              updated_date: new Date().toISOString()
            }
          : post
      ));
    } else {
      // Add new post
      const newPost = {
        id: Date.now().toString(),
        ...formData,
        created_date: new Date().toISOString(),
        updated_date: new Date().toISOString(),
        created_by_id: 'admin',
        created_by: '<EMAIL>',
        is_sample: false
      };
      setPosts(prev => [...prev, newPost]);
    }
    
    handleCloseModal();
  };

  const handleEdit = (post) => {
    setEditingPost(post);
    setFormData({
      title: post.title,
      slug: post.slug,
      excerpt: post.excerpt,
      content: post.content,
      category: post.category,
      tags: post.tags,
      read_time: post.read_time,
      published: post.published,
      featured_image_url: post.featured_image_url || ''
    });
    setIsModalOpen(true);
  };

  const handleDelete = (post) => {
    if (window.confirm(`Are you sure you want to delete "${post.title}"?`)) {
      setPosts(prev => prev.filter(p => p.id !== post.id));
    }
  };

  const handleView = (post) => {
    // For now, just open edit modal in view mode
    handleEdit(post);
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setEditingPost(null);
    setFormData({
      title: '',
      slug: '',
      excerpt: '',
      content: '',
      category: '',
      tags: [],
      read_time: 5,
      published: false,
      featured_image_url: ''
    });
  };

  const handleAddNew = () => {
    setEditingPost(null);
    setFormData({
      title: '',
      slug: '',
      excerpt: '',
      content: '',
      category: '',
      tags: [],
      read_time: 5,
      published: false,
      featured_image_url: ''
    });
    setIsModalOpen(true);
  };

  // Filter posts by category
  const filteredPosts = selectedCategory === 'all' 
    ? posts 
    : posts.filter(post => post.category === selectedCategory);

  const columns = [
    {
      key: 'title',
      label: 'TITLE',
      sortable: true,
      render: (title, post) => (
        <div>
          <div className="font-bold">{title}</div>
          <div className="text-sm text-gray-600">{post.slug}</div>
        </div>
      )
    },
    {
      key: 'category',
      label: 'CATEGORY',
      sortable: true,
      render: (category) => {
        const categoryData = blogCategories.find(cat => cat.value === category);
        return (
          <span className="px-2 py-1 text-xs font-bold bg-blue-100 text-blue-800 brutal-border">
            {categoryData?.label || category.toUpperCase()}
          </span>
        );
      }
    },
    {
      key: 'tags',
      label: 'TAGS',
      render: (tags) => (
        <div className="flex flex-wrap gap-1">
          {tags.slice(0, 2).map((tag, index) => (
            <span key={index} className="px-2 py-1 text-xs font-bold bg-gray-100 text-gray-800 brutal-border">
              {tag}
            </span>
          ))}
          {tags.length > 2 && (
            <span className="px-2 py-1 text-xs font-bold bg-gray-100 text-gray-800 brutal-border">
              +{tags.length - 2}
            </span>
          )}
        </div>
      )
    },
    {
      key: 'read_time',
      label: 'READ TIME',
      sortable: true,
      render: (time) => (
        <div className="flex items-center gap-1">
          <Clock className="w-4 h-4" />
          <span className="font-bold">{time} min</span>
        </div>
      )
    },
    {
      key: 'published',
      label: 'STATUS',
      sortable: true,
      render: (published) => (
        <span className={`px-2 py-1 text-xs font-bold brutal-border ${
          published ? 'bg-green-500 text-white' : 'bg-yellow-500 text-black'
        }`}>
          {published ? 'PUBLISHED' : 'DRAFT'}
        </span>
      )
    },
    {
      key: 'created_date',
      label: 'CREATED',
      sortable: true,
      render: (date) => (
        <span className="text-sm">
          {new Date(date).toLocaleDateString('en-GB')}
        </span>
      )
    }
  ];

  const getCategoryCounts = () => {
    const counts = { all: posts.length };
    blogCategories.forEach(category => {
      counts[category.value] = posts.filter(p => p.category === category.value).length;
    });
    return counts;
  };

  const categoryCounts = getCategoryCounts();
  const publishedCount = posts.filter(p => p.published).length;
  const draftCount = posts.filter(p => !p.published).length;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="bg-white brutal-border brutal-shadow p-6">
        <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
          <div>
            <h1 className="brutal-text text-3xl mb-2">BLOG MANAGEMENT</h1>
            <p className="font-bold text-gray-600">
              Create and manage blog posts, categories, and content
            </p>
          </div>
          
          <ActionButton 
            onClick={handleAddNew}
            icon={Plus}
            variant="primary"
          >
            ADD NEW POST
          </ActionButton>
        </div>
      </div>

      {/* Stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        <div className="bg-white brutal-border brutal-shadow p-6">
          <div className="flex items-center gap-4">
            <div className="bg-blue-500 text-white p-3 brutal-border brutal-shadow-small">
              <BookOpen className="w-6 h-6" />
            </div>
            <div>
              <div className="brutal-text text-2xl">{posts.length}</div>
              <div className="font-bold text-gray-600 text-sm">TOTAL POSTS</div>
            </div>
          </div>
        </div>
        
        <div className="bg-white brutal-border brutal-shadow p-6">
          <div className="flex items-center gap-4">
            <div className="bg-green-500 text-white p-3 brutal-border brutal-shadow-small">
              <Eye className="w-6 h-6" />
            </div>
            <div>
              <div className="brutal-text text-2xl">{publishedCount}</div>
              <div className="font-bold text-gray-600 text-sm">PUBLISHED</div>
            </div>
          </div>
        </div>
        
        <div className="bg-white brutal-border brutal-shadow p-6">
          <div className="flex items-center gap-4">
            <div className="bg-yellow-500 text-black p-3 brutal-border brutal-shadow-small">
              <Edit className="w-6 h-6" />
            </div>
            <div>
              <div className="brutal-text text-2xl">{draftCount}</div>
              <div className="font-bold text-gray-600 text-sm">DRAFTS</div>
            </div>
          </div>
        </div>
      </div>

      {/* Category Filter */}
      <div className="bg-white brutal-border brutal-shadow p-4">
        <div className="flex flex-wrap gap-2">
          <button
            onClick={() => setSelectedCategory('all')}
            className={`px-4 py-2 brutal-border brutal-shadow-small font-bold transition-all duration-150 hover:translate-x-1 hover:translate-y-1 hover:shadow-none ${
              selectedCategory === 'all'
                ? 'bg-black text-white'
                : 'bg-white text-black hover:bg-gray-50'
            }`}
          >
            ALL ({categoryCounts.all})
          </button>
          
          {blogCategories.map((category) => (
            <button
              key={category.value}
              onClick={() => setSelectedCategory(category.value)}
              className={`px-4 py-2 brutal-border brutal-shadow-small font-bold transition-all duration-150 hover:translate-x-1 hover:translate-y-1 hover:shadow-none ${
                selectedCategory === category.value
                  ? 'bg-blue-500 text-white'
                  : 'bg-white text-black hover:bg-gray-50'
              }`}
            >
              {category.label} ({categoryCounts[category.value] || 0})
            </button>
          ))}
        </div>
      </div>

      {/* Data Table */}
      <DataTable
        data={filteredPosts}
        columns={columns}
        onEdit={handleEdit}
        onDelete={handleDelete}
        onView={handleView}
        searchable={true}
        filterable={false}
      />

      {/* Add/Edit Modal */}
      <Modal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        title={editingPost ? 'EDIT BLOG POST' : 'ADD NEW BLOG POST'}
        size="full"
      >
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              label="Post Title"
              name="title"
              value={formData.title}
              onChange={handleInputChange}
              placeholder="Enter post title"
              required
            />
            
            <FormField
              label="URL Slug"
              name="slug"
              value={formData.slug}
              onChange={handleInputChange}
              placeholder="url-friendly-slug"
              required
            />
            
            <FormField
              label="Category"
              type="select"
              name="category"
              value={formData.category}
              onChange={handleInputChange}
              options={blogCategories}
              required
            />
            
            <FormField
              label="Read Time (minutes)"
              type="number"
              name="read_time"
              value={formData.read_time}
              onChange={handleInputChange}
              placeholder="5"
              required
            />
          </div>
          
          <FormField
            label="Excerpt"
            type="textarea"
            name="excerpt"
            value={formData.excerpt}
            onChange={handleInputChange}
            placeholder="Brief description of the post"
            rows={3}
            required
          />
          
          <FormField
            label="Content"
            type="textarea"
            name="content"
            value={formData.content}
            onChange={handleInputChange}
            placeholder="Write your blog post content here..."
            rows={12}
            required
          />
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              label="Tags (comma-separated)"
              name="tags"
              value={formData.tags.join(', ')}
              onChange={handleTagsChange}
              placeholder="e.g., marketing, strategy, tips"
            />
            
            <FormField
              label="Featured Image URL"
              name="featured_image_url"
              value={formData.featured_image_url}
              onChange={handleInputChange}
              placeholder="Enter featured image URL"
            />
          </div>
          
          <div className="flex items-center gap-6">
            <FormField
              label="Publish Post"
              type="checkbox"
              name="published"
              value={formData.published}
              onChange={handleInputChange}
            />
          </div>
          
          <div className="flex gap-4 pt-4">
            <ActionButton
              type="submit"
              variant="primary"
              icon={editingPost ? Edit : Plus}
            >
              {editingPost ? 'UPDATE POST' : 'CREATE POST'}
            </ActionButton>
            
            <ActionButton
              type="button"
              variant="outline"
              onClick={handleCloseModal}
            >
              CANCEL
            </ActionButton>
          </div>
        </form>
      </Modal>
    </div>
  );
};

export default AdminBlog;
