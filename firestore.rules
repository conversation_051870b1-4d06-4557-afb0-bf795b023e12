// Firestore Security Rules for on-generalservices
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isAdmin() {
      return isAuthenticated() && 
             exists(/databases/$(database)/documents/users/$(request.auth.uid)) &&
             get(/databases/$(database)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    function isOwner(userId) {
      return isAuthenticated() && request.auth.uid == userId;
    }
    
    function isValidUser() {
      return isAuthenticated() && 
             exists(/databases/$(database)/documents/users/$(request.auth.uid));
    }

    // Users collection - only admins can read/write user data
    match /users/{userId} {
      allow read, write: if isAdmin() || isOwner(userId);
      allow create: if isAuthenticated() && isOwner(userId);
    }

    // Gallery Images - Admin only for write, public read
    match /galleryImages/{imageId} {
      allow read: if true; // Public read access for gallery
      allow write: if isAdmin();
      allow create: if isAdmin();
      allow delete: if isAdmin();
    }

    // Blog Posts - Admin only for write, public read for published posts
    match /blogPosts/{postId} {
      allow read: if true; // Public read access for blog posts
      allow write: if isAdmin();
      allow create: if isAdmin();
      allow delete: if isAdmin();
    }

    // Case Studies - Admin only for write, public read
    match /caseStudies/{studyId} {
      allow read: if true; // Public read access for case studies
      allow write: if isAdmin();
      allow create: if isAdmin();
      allow delete: if isAdmin();
    }

    // Testimonials - Admin only for write, public read
    match /testimonials/{testimonialId} {
      allow read: if true; // Public read access for testimonials
      allow write: if isAdmin();
      allow create: if isAdmin();
      allow delete: if isAdmin();
    }

    // Resources - Admin only for write, public read
    match /resources/{resourceId} {
      allow read: if true; // Public read access for resources
      allow write: if isAdmin();
      allow create: if isAdmin();
      allow delete: if isAdmin();
    }

    // Contact Messages - Admin only access
    match /contactMessages/{messageId} {
      allow read: if isAdmin();
      allow write: if isAdmin();
      allow create: if true; // Anyone can create contact messages
      allow delete: if isAdmin();
    }

    // Settings - Admin only access
    match /settings/{settingId} {
      allow read: if isAdmin();
      allow write: if isAdmin();
      allow create: if isAdmin();
      allow delete: if isAdmin();
    }

    // Analytics/Stats - Admin only access
    match /analytics/{docId} {
      allow read: if isAdmin();
      allow write: if isAdmin();
      allow create: if isAdmin();
      allow delete: if isAdmin();
    }

    // Deny all other access
    match /{document=**} {
      allow read, write: if false;
    }
  }
}
