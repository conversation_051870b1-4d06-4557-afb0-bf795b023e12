import React, { createContext, useContext, useState, useEffect } from 'react';
import { AuthService } from '../services/firebase';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [user, setUser] = useState(null);

  // Listen to Firebase authentication state changes
  useEffect(() => {
    const unsubscribe = AuthService.onAuthStateChanged((user) => {
      if (user) {
        setUser(user);
        setIsAuthenticated(true);
      } else {
        setUser(null);
        setIsAuthenticated(false);
      }
      setIsLoading(false);
    });

    // Cleanup subscription on unmount
    return () => unsubscribe();
  }, []);

  const login = async (credentials) => {
    setIsLoading(true);

    try {
      // Use Firebase Authentication
      const { email, password } = credentials;
      const result = await AuthService.signIn(email, password);

      if (result.success) {
        // User state will be updated by the onAuthStateChanged listener
        return { success: true };
      } else {
        setIsLoading(false);
        return result;
      }
    } catch (error) {
      setIsLoading(false);
      return {
        success: false,
        error: 'Login failed. Please try again.'
      };
    }
  };

  const logout = async () => {
    try {
      await AuthService.signOut();
      // User state will be updated by the onAuthStateChanged listener
      return { success: true };
    } catch (error) {
      console.error('Logout error:', error);
      return {
        success: false,
        error: 'Failed to logout. Please try again.'
      };
    }
  };

  // Additional auth methods
  const createUser = async (userData) => {
    return await AuthService.createUser(userData);
  };

  const sendPasswordReset = async (email) => {
    return await AuthService.sendPasswordReset(email);
  };

  const updatePassword = async (currentPassword, newPassword) => {
    return await AuthService.updateUserPassword(currentPassword, newPassword);
  };

  const value = {
    isAuthenticated,
    isLoading,
    user,
    login,
    logout,
    createUser,
    sendPasswordReset,
    updatePassword
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export default AuthContext;
