# Firebase Deployment Guide for on-generalservices

## Prerequisites

1. **Firebase CLI Installation**
   ```bash
   npm install -g firebase-tools
   ```

2. **Firebase Project Setup**
   - Go to [Firebase Console](https://console.firebase.google.com/)
   - Create a new project or select existing project
   - Enable Authentication, Firestore Database, and Storage

## Step 1: Configure Environment Variables

1. Copy `.env.example` to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Update `.env` with your Firebase project credentials:
   ```env
   VITE_FIREBASE_API_KEY=your_api_key_here
   VITE_FIREBASE_AUTH_DOMAIN=your_project_id.firebaseapp.com
   VITE_FIREBASE_PROJECT_ID=your_project_id
   VITE_FIREBASE_STORAGE_BUCKET=your_project_id.appspot.com
   VITE_FIREBASE_MESSAGING_SENDER_ID=your_sender_id
   VITE_FIREBASE_APP_ID=your_app_id
   ```

## Step 2: Initialize Firebase in Your Project

1. Login to Firebase:
   ```bash
   firebase login
   ```

2. Initialize Firebase in your project:
   ```bash
   firebase init
   ```

3. Select the following services:
   - ✅ Firestore: Configure security rules and indexes
   - ✅ Storage: Configure security rules
   - ✅ Hosting (optional)

4. Choose your Firebase project when prompted

5. Configure Firestore:
   - Use existing `firestore.rules` file
   - Use existing `firestore.indexes.json` or create new

6. Configure Storage:
   - Use existing `storage.rules` file

## Step 3: Deploy Security Rules

1. **Deploy Firestore Rules:**
   ```bash
   firebase deploy --only firestore:rules
   ```

2. **Deploy Storage Rules:**
   ```bash
   firebase deploy --only storage:rules
   ```

3. **Deploy All Rules:**
   ```bash
   firebase deploy --only firestore:rules,storage:rules
   ```

## Step 4: Set Up Authentication

1. **Enable Email/Password Authentication:**
   - Go to Firebase Console → Authentication → Sign-in method
   - Enable "Email/Password" provider

2. **Create Admin User:**
   - Go to Authentication → Users
   - Add user manually with admin email and password
   - Note the User UID

3. **Set Admin Role in Firestore:**
   ```javascript
   // In Firebase Console → Firestore Database
   // Create collection: users
   // Create document with User UID as document ID
   {
     email: "<EMAIL>",
     role: "admin",
     name: "Admin User",
     createdAt: new Date(),
     lastLogin: null
   }
   ```

## Step 5: Test Firebase Integration

1. **Start Development Server:**
   ```bash
   npm run dev
   ```

2. **Test Authentication:**
   - Navigate to `/admin/login`
   - Login with admin credentials
   - Verify successful authentication

3. **Test Database Operations:**
   - Go to Admin Dashboard
   - Check if Firebase Setup component shows green status
   - Try adding/editing gallery images
   - Verify data appears in Firestore Console

4. **Test File Upload:**
   - In Gallery Management, try uploading an image
   - Verify file appears in Firebase Storage
   - Check if image URL is saved in Firestore

## Step 6: Data Migration (Optional)

1. **Run Data Migration:**
   - In Admin Dashboard, click "MIGRATE DATA" button
   - This will populate Firestore with dummy data
   - Check Firestore Console to verify data migration

## Security Rules Overview

### Firestore Rules (`firestore.rules`)
- **Admin-only write access** for all collections
- **Public read access** for gallery, blog, case studies, testimonials, resources
- **Anyone can create** contact messages
- **Role-based access control** using user documents

### Storage Rules (`storage.rules`)
- **Admin-only upload** permissions
- **Public read access** for all files
- **File type and size validation**
- **Organized folder structure**

## Troubleshooting

### Common Issues:

1. **Authentication Errors:**
   - Check if Email/Password provider is enabled
   - Verify admin user exists with correct role
   - Check environment variables

2. **Permission Denied Errors:**
   - Ensure security rules are deployed
   - Verify user has admin role in Firestore
   - Check if collections exist

3. **File Upload Errors:**
   - Verify Storage rules are deployed
   - Check file size limits (10MB for images)
   - Ensure correct file types

4. **Environment Variable Issues:**
   - Restart development server after changing .env
   - Verify all required variables are set
   - Check for typos in variable names

### Debug Commands:

```bash
# Check Firebase project status
firebase projects:list

# View current project
firebase use

# Test security rules locally
firebase emulators:start --only firestore,storage

# View deployment status
firebase deploy --dry-run
```

## Production Deployment

1. **Build for Production:**
   ```bash
   npm run build
   ```

2. **Deploy to Firebase Hosting (Optional):**
   ```bash
   firebase deploy --only hosting
   ```

3. **Deploy Everything:**
   ```bash
   firebase deploy
   ```

## Monitoring and Maintenance

1. **Monitor Usage:**
   - Check Firebase Console → Usage tab
   - Monitor Firestore reads/writes
   - Track Storage usage

2. **Security Monitoring:**
   - Review Authentication logs
   - Monitor failed login attempts
   - Check Firestore security rule violations

3. **Backup Strategy:**
   - Set up automated Firestore exports
   - Regular Storage backups
   - Document admin user credentials securely

## Next Steps

After successful deployment:

1. **Test all admin functionality**
2. **Create additional admin users if needed**
3. **Set up monitoring and alerts**
4. **Configure backup strategies**
5. **Update contact form email notifications**
6. **Optimize Firestore queries for better performance**

## Support

For issues with Firebase integration:
1. Check Firebase Console for error logs
2. Review browser console for JavaScript errors
3. Verify security rules in Firebase Console
4. Test with Firebase Emulator for local debugging
