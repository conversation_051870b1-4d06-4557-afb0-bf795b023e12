import React from 'react';
import { Link } from 'react-router-dom';

const Button = ({ 
  children, 
  variant = 'primary', 
  size = 'medium', 
  href, 
  to, 
  onClick, 
  disabled = false, 
  className = '', 
  icon: Icon,
  ...props 
}) => {
  const baseClasses = 'brutal-border brutal-shadow brutal-text transition-all duration-150 hover:translate-x-2 hover:translate-y-2 hover:shadow-none inline-flex items-center justify-center gap-2 font-bold';
  
  const variants = {
    primary: 'bg-blue-500 text-white',
    secondary: 'bg-yellow-400 text-black',
    accent: 'bg-pink-500 text-white',
    success: 'bg-green-400 text-black',
    danger: 'bg-red-500 text-white',
    dark: 'bg-black text-white',
    outline: 'bg-white text-black border-black'
  };

  const sizes = {
    small: 'px-4 py-2 text-sm',
    medium: 'px-6 py-3 text-sm',
    large: 'px-8 py-4 text-lg'
  };

  const classes = `${baseClasses} ${variants[variant]} ${sizes[size]} ${disabled ? 'opacity-50 cursor-not-allowed' : ''} ${className}`;

  const content = (
    <>
      {Icon && <Icon className="w-4 h-4" />}
      {children}
    </>
  );

  if (to) {
    return (
      <Link to={to} className={classes} {...props}>
        {content}
      </Link>
    );
  }

  if (href) {
    return (
      <a href={href} className={classes} {...props}>
        {content}
      </a>
    );
  }

  return (
    <button 
      onClick={onClick} 
      disabled={disabled} 
      className={classes} 
      {...props}
    >
      {content}
    </button>
  );
};

export default Button;
