import React from 'react';

const FormField = ({ 
  label, 
  type = 'text', 
  name, 
  value, 
  onChange, 
  placeholder, 
  required = false, 
  error, 
  options = [], 
  rows = 4,
  className = '',
  disabled = false,
  accept,
  multiple = false
}) => {
  const baseInputClasses = `w-full px-4 py-3 brutal-border brutal-shadow-small focus:outline-none focus:translate-x-1 focus:translate-y-1 focus:shadow-none transition-all duration-150 font-bold ${disabled ? 'bg-gray-100 cursor-not-allowed' : 'bg-white'}`;

  const renderInput = () => {
    switch (type) {
      case 'textarea':
        return (
          <textarea
            name={name}
            value={value}
            onChange={onChange}
            placeholder={placeholder}
            required={required}
            rows={rows}
            disabled={disabled}
            className={`${baseInputClasses} resize-none ${className}`}
          />
        );

      case 'select':
        return (
          <select
            name={name}
            value={value}
            onChange={onChange}
            required={required}
            disabled={disabled}
            className={`${baseInputClasses} ${className}`}
          >
            <option value="">Select {label}</option>
            {options.map((option) => (
              <option key={option.value} value={option.value}>
                {option.label}
              </option>
            ))}
          </select>
        );

      case 'checkbox':
        return (
          <div className="flex items-center gap-3">
            <input
              type="checkbox"
              name={name}
              checked={value}
              onChange={onChange}
              required={required}
              disabled={disabled}
              className="w-5 h-5 brutal-border brutal-shadow-small focus:outline-none"
            />
            <span className="font-bold">{label}</span>
          </div>
        );

      case 'radio':
        return (
          <div className="space-y-2">
            {options.map((option) => (
              <div key={option.value} className="flex items-center gap-3">
                <input
                  type="radio"
                  name={name}
                  value={option.value}
                  checked={value === option.value}
                  onChange={onChange}
                  required={required}
                  disabled={disabled}
                  className="w-5 h-5 brutal-border brutal-shadow-small focus:outline-none"
                />
                <span className="font-bold">{option.label}</span>
              </div>
            ))}
          </div>
        );

      case 'file':
        return (
          <input
            type="file"
            name={name}
            onChange={onChange}
            accept={accept}
            multiple={multiple}
            required={required}
            disabled={disabled}
            className={`${baseInputClasses} file:mr-4 file:py-2 file:px-4 file:brutal-border file:brutal-shadow-small file:bg-yellow-400 file:text-black file:font-bold file:border-0 ${className}`}
          />
        );

      default:
        return (
          <input
            type={type}
            name={name}
            value={value}
            onChange={onChange}
            placeholder={placeholder}
            required={required}
            disabled={disabled}
            className={`${baseInputClasses} ${className}`}
          />
        );
    }
  };

  return (
    <div className="space-y-2">
      {type !== 'checkbox' && label && (
        <label className="block font-bold text-black">
          {label}
          {required && <span className="text-red-500 ml-1">*</span>}
        </label>
      )}
      
      {renderInput()}
      
      {error && (
        <div className="bg-red-500 text-white px-3 py-2 brutal-border brutal-shadow-small">
          <span className="font-bold">{error}</span>
        </div>
      )}
    </div>
  );
};

export default FormField;
