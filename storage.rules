// Firebase Storage Security Rules for on-generalservices
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    
    // Helper functions
    function isAuthenticated() {
      return request.auth != null;
    }
    
    function isAdmin() {
      return isAuthenticated() && 
             firestore.exists(/databases/(default)/documents/users/$(request.auth.uid)) &&
             firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.role == 'admin';
    }
    
    function isValidImageFile() {
      return resource.contentType.matches('image/.*') &&
             resource.size < 10 * 1024 * 1024; // 10MB limit
    }
    
    function isValidDocumentFile() {
      return (resource.contentType.matches('application/pdf') ||
              resource.contentType.matches('application/msword') ||
              resource.contentType.matches('application/vnd.openxmlformats-officedocument.wordprocessingml.document') ||
              resource.contentType.matches('text/plain')) &&
             resource.size < 50 * 1024 * 1024; // 50MB limit for documents
    }

    // Gallery images - Admin upload, public read
    match /gallery/{imageId} {
      allow read: if true; // Public read access
      allow write: if isAdmin() && isValidImageFile();
      allow delete: if isAdmin();
    }

    // Blog images - Admin upload, public read
    match /blog/{imageId} {
      allow read: if true; // Public read access
      allow write: if isAdmin() && isValidImageFile();
      allow delete: if isAdmin();
    }

    // Case study images - Admin upload, public read
    match /case-studies/{imageId} {
      allow read: if true; // Public read access
      allow write: if isAdmin() && isValidImageFile();
      allow delete: if isAdmin();
    }

    // Testimonial images - Admin upload, public read
    match /testimonials/{imageId} {
      allow read: if true; // Public read access
      allow write: if isAdmin() && isValidImageFile();
      allow delete: if isAdmin();
    }

    // Resource files - Admin upload, public read
    match /resources/{fileId} {
      allow read: if true; // Public read access for downloads
      allow write: if isAdmin() && (isValidImageFile() || isValidDocumentFile());
      allow delete: if isAdmin();
    }

    // User avatars - Admin upload, public read
    match /avatars/{userId} {
      allow read: if true; // Public read access
      allow write: if isAdmin() && isValidImageFile();
      allow delete: if isAdmin();
    }

    // Temporary uploads - Admin only
    match /temp/{fileId} {
      allow read: if isAdmin();
      allow write: if isAdmin();
      allow delete: if isAdmin();
    }

    // Deny all other access
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
